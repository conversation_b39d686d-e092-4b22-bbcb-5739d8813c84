const fs = require('fs');
const ExcelJS = require('exceljs');
const { JSDOM } = require('jsdom');

class JsonToExcelConverter {
    constructor() {
        this.excelColumns = [
            { header: 'Postcode', key: 'postcode', width: 15 },
            { header: 'Company Name', key: 'name', width: 30 },
            { header: 'Image', key: 'image', width: 20 },
            { header: 'Address', key: 'address', width: 50 },
            { header: 'Distance', key: 'distance', width: 15 },
            { header: 'Phone', key: 'phone', width: 15 },
            { header: 'Website', key: 'website', width: 30 },
            { header: 'Disciplines', key: 'disciplines', width: 30 },
            { header: 'Status', key: 'status', width: 10 },
            { header: 'Company Bio', key: 'bio', width: 100 },
            { header: 'Latitude', key: 'lat', width: 15 },
            { header: 'Longitude', key: 'lon', width: 15 },
            { header: 'Raw Data', key: 'rawData', width: 150 }
        ];

        this.workbook = new ExcelJS.Workbook();
        this.worksheet = this.workbook.addWorksheet('Contractors');
        this.worksheet.columns = this.excelColumns;
    }

    parseContractorData(html) {
        const dom = new JSDOM(html);
        const doc = dom.window.document;

        return {
            image: this.getImageData(doc),
            address: this.getTextContent(doc, "Address:"),
            distance: this.getTextContent(doc, "Distance From Point:"),
            phone: this.getTextContent(doc, "Phone:"),
            website: this.getTextContent(doc, "Website:"),
            disciplines: this.getTextContent(doc, "Disciplines:"),
            status: this.getTextContent(doc, "Status:"),
            bio: this.getTextContent(doc, "Company Bio:")
        };
    }

    getImageData(doc) {
        const imgElement = doc.querySelector('label img');
        if (imgElement && imgElement.src && imgElement.src.startsWith('data:image/')) {
            return imgElement.src;
        }
        return '';
    }

    getTextContent(doc, label) {
        const labelElement = Array.from(doc.querySelectorAll('label')).find(el =>
            el.textContent.includes(label) && !el.querySelector('img')
        );
        return labelElement ? labelElement.nextSibling.textContent.trim() : '';
    }

    parseJsonToExcel(jsonData) {
        const allContractors = [];

        for (const item of jsonData.data) {
            const postcode = item.postcode;
            
            if (!item.hasData || !item.rawResponse || !item.rawResponse.contractorPoints) {
                // No data case
                allContractors.push({
                    postcode,
                    name: '',
                    image: '',
                    address: '',
                    distance: '',
                    phone: '',
                    website: '',
                    disciplines: '',
                    status: '',
                    bio: '',
                    lat: '',
                    lon: '',
                    rawData: JSON.stringify({
                        postcode,
                        status: item.error ? 'error' : 'no_data',
                        error: item.error || null,
                        timestamp: item.timestamp
                    })
                });
                continue;
            }

            // Parse contractors
            for (const [name, data] of Object.entries(item.rawResponse.contractorPoints)) {
                const parsedData = this.parseContractorData(data.body);
                
                const compactRawData = {
                    postcode,
                    contractorName: name,
                    lat: data.lat,
                    lon: data.lon,
                    body: data.body,
                    timestamp: item.timestamp
                };
                
                allContractors.push({
                    postcode,
                    name,
                    ...parsedData,
                    lat: data.lat || '',
                    lon: data.lon || '',
                    rawData: JSON.stringify(compactRawData)
                });
            }
        }

        return allContractors;
    }

    addToExcel(contractors) {
        contractors.forEach(contractor => {
            this.worksheet.addRow({
                postcode: contractor.postcode || '',
                name: contractor.name || '',
                image: contractor.image || '',
                address: contractor.address || '',
                distance: contractor.distance || '',
                phone: contractor.phone || '',
                website: contractor.website || '',
                disciplines: contractor.disciplines || '',
                status: contractor.status || '',
                bio: contractor.bio || '',
                lat: contractor.lat || '',
                lon: contractor.lon || '',
                rawData: contractor.rawData || ''
            });
        });
    }

    loadSeparateFiles(baseFilename) {
        const directory = require('path').dirname(baseFilename);
        const baseName = require('path').basename(baseFilename, '.json');

        const separateFiles = [];

        if (fs.existsSync(directory)) {
            const files = fs.readdirSync(directory);
            const pattern = `${baseName}_.*_raw.json`;

            files.forEach(file => {
                if (file.match(new RegExp(pattern.replace('.*', '.*')))) {
                    const fullPath = require('path').join(directory, file);
                    try {
                        const data = JSON.parse(fs.readFileSync(fullPath));
                        separateFiles.push(data);
                        console.log(`Loaded separate file: ${file}`);
                    } catch (error) {
                        console.log(`Error loading ${file}:`, error.message);
                    }
                }
            });
        }

        return separateFiles;
    }

    async convert(jsonFilename, excelFilename) {
        try {
            console.log(`Reading JSON file: ${jsonFilename}`);
            const jsonData = JSON.parse(fs.readFileSync(jsonFilename));

            // Load separate files for large responses
            console.log(`Looking for separate files...`);
            const separateFiles = this.loadSeparateFiles(jsonFilename);

            // Merge separate files into main data
            separateFiles.forEach(separateData => {
                const existingIndex = jsonData.data.findIndex(item => item.postcode === separateData.postcode);
                if (existingIndex >= 0) {
                    // Replace with full data from separate file
                    jsonData.data[existingIndex] = {
                        postcode: separateData.postcode,
                        timestamp: separateData.timestamp,
                        hasData: !!(separateData.rawResponse && separateData.rawResponse.contractorPoints),
                        rawResponse: separateData.rawResponse
                    };
                    console.log(`Merged data for ${separateData.postcode} from separate file`);
                }
            });

            console.log(`Converting ${jsonData.totalPostcodes} postcodes to Excel...`);
            const contractors = this.parseJsonToExcel(jsonData);

            console.log(`Adding ${contractors.length} contractors to Excel...`);
            this.addToExcel(contractors);

            console.log(`Saving Excel file: ${excelFilename}`);
            await this.workbook.xlsx.writeFile(excelFilename);

            console.log(`Done! Converted ${contractors.length} contractors to ${excelFilename}`);
            console.log(`Processed ${separateFiles.length} separate files`);

        } catch (error) {
            console.error('Error converting JSON to Excel:', error);
            process.exit(1);
        }
    }
}

// Usage
const jsonFilename = process.argv[2];
if (!jsonFilename) {
    console.log('Usage: node json-to-excel.js <json-filename>');
    console.log('Example: node json-to-excel.js ./exports/raw_data_2024-01-01.json');
    process.exit(1);
}

const excelFilename = jsonFilename.replace('.json', '.xlsx');
const converter = new JsonToExcelConverter();
converter.convert(jsonFilename, excelFilename);
