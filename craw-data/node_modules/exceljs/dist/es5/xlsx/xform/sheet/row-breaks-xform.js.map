{"version": 3, "file": "row-breaks-xform.js", "names": ["PageBreaksXform", "require", "ListXform", "RowBreaksXform", "constructor", "options", "tag", "count", "childXform", "render", "xmlStream", "model", "length", "openNode", "$", "addAttribute", "$count", "for<PERSON>ach", "childModel", "closeNode", "empty", "leafNode", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/sheet/row-breaks-xform.js"], "sourcesContent": ["'use strict';\n\nconst PageBreaksXform = require('./page-breaks-xform');\n\nconst ListXform = require('../list-xform');\n\nclass RowBreaksXform extends ListXform {\n  constructor() {\n    const options = {\n      tag: 'rowBreaks',\n      count: true,\n      childXform: new PageBreaksXform(),\n    };\n    super(options);\n  }\n\n  // get tag() { return 'rowBreaks'; }\n\n  render(xmlStream, model) {\n    if (model && model.length) {\n      xmlStream.openNode(this.tag, this.$);\n      if (this.count) {\n        xmlStream.addAttribute(this.$count, model.length);\n        xmlStream.addAttribute('manualBreakCount', model.length);\n      }\n\n      const {childXform} = this;\n      model.forEach(childModel => {\n        childXform.render(xmlStream, childModel);\n      });\n\n      xmlStream.closeNode();\n    } else if (this.empty) {\n      xmlStream.leafNode(this.tag);\n    }\n  }\n}\n\nmodule.exports = RowBreaksXform;\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,eAAe,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAEtD,MAAMC,SAAS,GAAGD,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAME,cAAc,SAASD,SAAS,CAAC;EACrCE,WAAWA,CAAA,EAAG;IACZ,MAAMC,OAAO,GAAG;MACdC,GAAG,EAAE,WAAW;MAChBC,KAAK,EAAE,IAAI;MACXC,UAAU,EAAE,IAAIR,eAAe,CAAC;IAClC,CAAC;IACD,KAAK,CAACK,OAAO,CAAC;EAChB;;EAEA;;EAEAI,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvB,IAAIA,KAAK,IAAIA,KAAK,CAACC,MAAM,EAAE;MACzBF,SAAS,CAACG,QAAQ,CAAC,IAAI,CAACP,GAAG,EAAE,IAAI,CAACQ,CAAC,CAAC;MACpC,IAAI,IAAI,CAACP,KAAK,EAAE;QACdG,SAAS,CAACK,YAAY,CAAC,IAAI,CAACC,MAAM,EAAEL,KAAK,CAACC,MAAM,CAAC;QACjDF,SAAS,CAACK,YAAY,CAAC,kBAAkB,EAAEJ,KAAK,CAACC,MAAM,CAAC;MAC1D;MAEA,MAAM;QAACJ;MAAU,CAAC,GAAG,IAAI;MACzBG,KAAK,CAACM,OAAO,CAACC,UAAU,IAAI;QAC1BV,UAAU,CAACC,MAAM,CAACC,SAAS,EAAEQ,UAAU,CAAC;MAC1C,CAAC,CAAC;MAEFR,SAAS,CAACS,SAAS,CAAC,CAAC;IACvB,CAAC,MAAM,IAAI,IAAI,CAACC,KAAK,EAAE;MACrBV,SAAS,CAACW,QAAQ,CAAC,IAAI,CAACf,GAAG,CAAC;IAC9B;EACF;AACF;AAEAgB,MAAM,CAACC,OAAO,GAAGpB,cAAc"}