{"version": 3, "file": "outline-properties-xform.js", "names": ["BaseXform", "require", "isDefined", "attr", "OutlinePropertiesXform", "tag", "render", "xmlStream", "model", "summaryBelow", "summaryRight", "leafNode", "Number", "undefined", "parseOpen", "node", "name", "attributes", "Boolean", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/sheet/outline-properties-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nconst isDefined = attr => typeof attr !== 'undefined';\n\nclass OutlinePropertiesXform extends BaseXform {\n  get tag() {\n    return 'outlinePr';\n  }\n\n  render(xmlStream, model) {\n    if (model && (isDefined(model.summaryBelow) || isDefined(model.summaryRight))) {\n      xmlStream.leafNode(this.tag, {\n        summaryBelow: isDefined(model.summaryBelow) ? Number(model.summaryBelow) : undefined,\n        summaryRight: isDefined(model.summaryRight) ? Number(model.summaryRight) : undefined,\n      });\n      return true;\n    }\n    return false;\n  }\n\n  parseOpen(node) {\n    if (node.name === this.tag) {\n      this.model = {\n        summaryBelow: isDefined(node.attributes.summaryBelow)\n          ? Boolean(Number(node.attributes.summaryBelow))\n          : undefined,\n        summaryRight: isDefined(node.attributes.summaryRight)\n          ? Boolean(Number(node.attributes.summaryRight))\n          : undefined,\n      };\n      return true;\n    }\n    return false;\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n}\n\nmodule.exports = OutlinePropertiesXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,SAAS,GAAGC,IAAI,IAAI,OAAOA,IAAI,KAAK,WAAW;AAErD,MAAMC,sBAAsB,SAASJ,SAAS,CAAC;EAC7C,IAAIK,GAAGA,CAAA,EAAG;IACR,OAAO,WAAW;EACpB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvB,IAAIA,KAAK,KAAKN,SAAS,CAACM,KAAK,CAACC,YAAY,CAAC,IAAIP,SAAS,CAACM,KAAK,CAACE,YAAY,CAAC,CAAC,EAAE;MAC7EH,SAAS,CAACI,QAAQ,CAAC,IAAI,CAACN,GAAG,EAAE;QAC3BI,YAAY,EAAEP,SAAS,CAACM,KAAK,CAACC,YAAY,CAAC,GAAGG,MAAM,CAACJ,KAAK,CAACC,YAAY,CAAC,GAAGI,SAAS;QACpFH,YAAY,EAAER,SAAS,CAACM,KAAK,CAACE,YAAY,CAAC,GAAGE,MAAM,CAACJ,KAAK,CAACE,YAAY,CAAC,GAAGG;MAC7E,CAAC,CAAC;MACF,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAIA,IAAI,CAACC,IAAI,KAAK,IAAI,CAACX,GAAG,EAAE;MAC1B,IAAI,CAACG,KAAK,GAAG;QACXC,YAAY,EAAEP,SAAS,CAACa,IAAI,CAACE,UAAU,CAACR,YAAY,CAAC,GACjDS,OAAO,CAACN,MAAM,CAACG,IAAI,CAACE,UAAU,CAACR,YAAY,CAAC,CAAC,GAC7CI,SAAS;QACbH,YAAY,EAAER,SAAS,CAACa,IAAI,CAACE,UAAU,CAACP,YAAY,CAAC,GACjDQ,OAAO,CAACN,MAAM,CAACG,IAAI,CAACE,UAAU,CAACP,YAAY,CAAC,CAAC,GAC7CG;MACN,CAAC;MACD,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAM,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGlB,sBAAsB"}