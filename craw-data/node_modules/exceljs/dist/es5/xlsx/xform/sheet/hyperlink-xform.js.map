{"version": 3, "file": "hyperlink-xform.js", "names": ["BaseXform", "require", "HyperlinkXform", "tag", "render", "xmlStream", "model", "isInternalLink", "leafNode", "ref", "address", "rId", "tooltip", "location", "target", "parseOpen", "node", "name", "attributes", "parseText", "parseClose", "test", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/sheet/hyperlink-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nclass HyperlinkXform extends BaseXform {\n  get tag() {\n    return 'hyperlink';\n  }\n\n  render(xmlStream, model) {\n    if (this.isInternalLink(model)) {\n      xmlStream.leafNode('hyperlink', {\n        ref: model.address,\n        'r:id': model.rId,\n        tooltip: model.tooltip,\n        location: model.target,\n      });\n    } else {\n      xmlStream.leafNode('hyperlink', {\n        ref: model.address,\n        'r:id': model.rId,\n        tooltip: model.tooltip,\n      });\n    }\n  }\n\n  parseOpen(node) {\n    if (node.name === 'hyperlink') {\n      this.model = {\n        address: node.attributes.ref,\n        rId: node.attributes['r:id'],\n        tooltip: node.attributes.tooltip,\n      };\n\n      // This is an internal link\n      if (node.attributes.location) {\n        this.model.target = node.attributes.location;\n      }\n      return true;\n    }\n    return false;\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n\n  isInternalLink(model) {\n    // @example: Sheet2!D3, return true\n    return model.target && /^[^!]+![a-zA-Z]+[\\d]+$/.test(model.target);\n  }\n}\n\nmodule.exports = HyperlinkXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,cAAc,SAASF,SAAS,CAAC;EACrC,IAAIG,GAAGA,CAAA,EAAG;IACR,OAAO,WAAW;EACpB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvB,IAAI,IAAI,CAACC,cAAc,CAACD,KAAK,CAAC,EAAE;MAC9BD,SAAS,CAACG,QAAQ,CAAC,WAAW,EAAE;QAC9BC,GAAG,EAAEH,KAAK,CAACI,OAAO;QAClB,MAAM,EAAEJ,KAAK,CAACK,GAAG;QACjBC,OAAO,EAAEN,KAAK,CAACM,OAAO;QACtBC,QAAQ,EAAEP,KAAK,CAACQ;MAClB,CAAC,CAAC;IACJ,CAAC,MAAM;MACLT,SAAS,CAACG,QAAQ,CAAC,WAAW,EAAE;QAC9BC,GAAG,EAAEH,KAAK,CAACI,OAAO;QAClB,MAAM,EAAEJ,KAAK,CAACK,GAAG;QACjBC,OAAO,EAAEN,KAAK,CAACM;MACjB,CAAC,CAAC;IACJ;EACF;EAEAG,SAASA,CAACC,IAAI,EAAE;IACd,IAAIA,IAAI,CAACC,IAAI,KAAK,WAAW,EAAE;MAC7B,IAAI,CAACX,KAAK,GAAG;QACXI,OAAO,EAAEM,IAAI,CAACE,UAAU,CAACT,GAAG;QAC5BE,GAAG,EAAEK,IAAI,CAACE,UAAU,CAAC,MAAM,CAAC;QAC5BN,OAAO,EAAEI,IAAI,CAACE,UAAU,CAACN;MAC3B,CAAC;;MAED;MACA,IAAII,IAAI,CAACE,UAAU,CAACL,QAAQ,EAAE;QAC5B,IAAI,CAACP,KAAK,CAACQ,MAAM,GAAGE,IAAI,CAACE,UAAU,CAACL,QAAQ;MAC9C;MACA,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAM,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;EAEAb,cAAcA,CAACD,KAAK,EAAE;IACpB;IACA,OAAOA,KAAK,CAACQ,MAAM,IAAI,wBAAwB,CAACO,IAAI,CAACf,KAAK,CAACQ,MAAM,CAAC;EACpE;AACF;AAEAQ,MAAM,CAACC,OAAO,GAAGrB,cAAc"}