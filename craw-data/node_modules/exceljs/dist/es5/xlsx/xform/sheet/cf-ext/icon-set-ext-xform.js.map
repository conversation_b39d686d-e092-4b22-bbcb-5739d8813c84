{"version": 3, "file": "icon-set-ext-xform.js", "names": ["BaseXform", "require", "CompositeXform", "CfvoExtXform", "CfIconExtXform", "IconSetExtXform", "constructor", "map", "cfvoXform", "cfIconXform", "tag", "render", "xmlStream", "model", "openNode", "iconSet", "toStringAttribute", "reverse", "toBoolAttribute", "showValue", "custom", "icons", "cfvo", "for<PERSON>ach", "icon", "i", "iconId", "closeNode", "createNewModel", "_ref", "attributes", "toStringValue", "toBoolValue", "onParserClose", "name", "parser", "prop", "split", "push", "module", "exports"], "sources": ["../../../../../../lib/xlsx/xform/sheet/cf-ext/icon-set-ext-xform.js"], "sourcesContent": ["const BaseXform = require('../../base-xform');\nconst CompositeXform = require('../../composite-xform');\n\nconst CfvoExtXform = require('./cfvo-ext-xform');\nconst CfIconExtXform = require('./cf-icon-ext-xform');\n\nclass IconSetExtXform extends CompositeXform {\n  constructor() {\n    super();\n\n    this.map = {\n      'x14:cfvo': (this.cfvoXform = new CfvoExtXform()),\n      'x14:cfIcon': (this.cfIconXform = new CfIconExtXform()),\n    };\n  }\n\n  get tag() {\n    return 'x14:iconSet';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openNode(this.tag, {\n      iconSet: BaseXform.toStringAttribute(model.iconSet),\n      reverse: BaseXform.toBoolAttribute(model.reverse, false),\n      showValue: BaseXform.toBoolAttribute(model.showValue, true),\n      custom: BaseXform.toBoolAttribute(model.icons, false),\n    });\n\n    model.cfvo.forEach(cfvo => {\n      this.cfvoXform.render(xmlStream, cfvo);\n    });\n\n    if (model.icons) {\n      model.icons.forEach((icon, i) => {\n        icon.iconId = i;\n        this.cfIconXform.render(xmlStream, icon);\n      });\n    }\n\n    xmlStream.closeNode();\n  }\n\n  createNewModel({attributes}) {\n    return {\n      cfvo: [],\n      iconSet: BaseXform.toStringValue(attributes.iconSet, '3TrafficLights'),\n      reverse: BaseXform.toBoolValue(attributes.reverse, false),\n      showValue: BaseXform.toBoolValue(attributes.showValue, true),\n    };\n  }\n\n  onParserClose(name, parser) {\n    const [, prop] = name.split(':');\n    switch (prop) {\n      case 'cfvo':\n        this.model.cfvo.push(parser.model);\n        break;\n\n      case 'cfIcon':\n        if (!this.model.icons) {\n          this.model.icons = [];\n        }\n        this.model.icons.push(parser.model);\n        break;\n\n      default:\n        this.model[prop] = parser.model;\n        break;\n    }\n  }\n}\n\nmodule.exports = IconSetExtXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAC7C,MAAMC,cAAc,GAAGD,OAAO,CAAC,uBAAuB,CAAC;AAEvD,MAAME,YAAY,GAAGF,OAAO,CAAC,kBAAkB,CAAC;AAChD,MAAMG,cAAc,GAAGH,OAAO,CAAC,qBAAqB,CAAC;AAErD,MAAMI,eAAe,SAASH,cAAc,CAAC;EAC3CI,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACT,UAAU,EAAG,IAAI,CAACC,SAAS,GAAG,IAAIL,YAAY,CAAC,CAAE;MACjD,YAAY,EAAG,IAAI,CAACM,WAAW,GAAG,IAAIL,cAAc,CAAC;IACvD,CAAC;EACH;EAEA,IAAIM,GAAGA,CAAA,EAAG;IACR,OAAO,aAAa;EACtB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACJ,GAAG,EAAE;MAC3BK,OAAO,EAAEf,SAAS,CAACgB,iBAAiB,CAACH,KAAK,CAACE,OAAO,CAAC;MACnDE,OAAO,EAAEjB,SAAS,CAACkB,eAAe,CAACL,KAAK,CAACI,OAAO,EAAE,KAAK,CAAC;MACxDE,SAAS,EAAEnB,SAAS,CAACkB,eAAe,CAACL,KAAK,CAACM,SAAS,EAAE,IAAI,CAAC;MAC3DC,MAAM,EAAEpB,SAAS,CAACkB,eAAe,CAACL,KAAK,CAACQ,KAAK,EAAE,KAAK;IACtD,CAAC,CAAC;IAEFR,KAAK,CAACS,IAAI,CAACC,OAAO,CAACD,IAAI,IAAI;MACzB,IAAI,CAACd,SAAS,CAACG,MAAM,CAACC,SAAS,EAAEU,IAAI,CAAC;IACxC,CAAC,CAAC;IAEF,IAAIT,KAAK,CAACQ,KAAK,EAAE;MACfR,KAAK,CAACQ,KAAK,CAACE,OAAO,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAK;QAC/BD,IAAI,CAACE,MAAM,GAAGD,CAAC;QACf,IAAI,CAAChB,WAAW,CAACE,MAAM,CAACC,SAAS,EAAEY,IAAI,CAAC;MAC1C,CAAC,CAAC;IACJ;IAEAZ,SAAS,CAACe,SAAS,CAAC,CAAC;EACvB;EAEAC,cAAcA,CAAAC,IAAA,EAAe;IAAA,IAAd;MAACC;IAAU,CAAC,GAAAD,IAAA;IACzB,OAAO;MACLP,IAAI,EAAE,EAAE;MACRP,OAAO,EAAEf,SAAS,CAAC+B,aAAa,CAACD,UAAU,CAACf,OAAO,EAAE,gBAAgB,CAAC;MACtEE,OAAO,EAAEjB,SAAS,CAACgC,WAAW,CAACF,UAAU,CAACb,OAAO,EAAE,KAAK,CAAC;MACzDE,SAAS,EAAEnB,SAAS,CAACgC,WAAW,CAACF,UAAU,CAACX,SAAS,EAAE,IAAI;IAC7D,CAAC;EACH;EAEAc,aAAaA,CAACC,IAAI,EAAEC,MAAM,EAAE;IAC1B,MAAM,GAAGC,IAAI,CAAC,GAAGF,IAAI,CAACG,KAAK,CAAC,GAAG,CAAC;IAChC,QAAQD,IAAI;MACV,KAAK,MAAM;QACT,IAAI,CAACvB,KAAK,CAACS,IAAI,CAACgB,IAAI,CAACH,MAAM,CAACtB,KAAK,CAAC;QAClC;MAEF,KAAK,QAAQ;QACX,IAAI,CAAC,IAAI,CAACA,KAAK,CAACQ,KAAK,EAAE;UACrB,IAAI,CAACR,KAAK,CAACQ,KAAK,GAAG,EAAE;QACvB;QACA,IAAI,CAACR,KAAK,CAACQ,KAAK,CAACiB,IAAI,CAACH,MAAM,CAACtB,KAAK,CAAC;QACnC;MAEF;QACE,IAAI,CAACA,KAAK,CAACuB,IAAI,CAAC,GAAGD,MAAM,CAACtB,KAAK;QAC/B;IACJ;EACF;AACF;AAEA0B,MAAM,CAACC,OAAO,GAAGnC,eAAe"}