{"version": 3, "file": "sheet-properties-xform.js", "names": ["BaseXform", "require", "ColorXform", "PageSetupPropertiesXform", "OutlinePropertiesXform", "SheetPropertiesXform", "constructor", "map", "tabColor", "pageSetUpPr", "outlinePr", "tag", "render", "xmlStream", "model", "add<PERSON><PERSON><PERSON>", "openNode", "inner", "pageSetup", "outlineProperties", "closeNode", "commit", "rollback", "parseOpen", "node", "parser", "name", "reset", "parseText", "text", "parseClose", "undefined", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/sheet/sheet-properties-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\nconst ColorXform = require('../style/color-xform');\nconst PageSetupPropertiesXform = require('./page-setup-properties-xform');\nconst OutlinePropertiesXform = require('./outline-properties-xform');\n\nclass SheetPropertiesXform extends BaseXform {\n  constructor() {\n    super();\n\n    this.map = {\n      tabColor: new ColorXform('tabColor'),\n      pageSetUpPr: new PageSetupPropertiesXform(),\n      outlinePr: new OutlinePropertiesXform(),\n    };\n  }\n\n  get tag() {\n    return 'sheetPr';\n  }\n\n  render(xmlStream, model) {\n    if (model) {\n      xmlStream.addRollback();\n      xmlStream.openNode('sheetPr');\n\n      let inner = false;\n      inner = this.map.tabColor.render(xmlStream, model.tabColor) || inner;\n      inner = this.map.pageSetUpPr.render(xmlStream, model.pageSetup) || inner;\n      inner = this.map.outlinePr.render(xmlStream, model.outlineProperties) || inner;\n\n      if (inner) {\n        xmlStream.closeNode();\n        xmlStream.commit();\n      } else {\n        xmlStream.rollback();\n      }\n    }\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    if (node.name === this.tag) {\n      this.reset();\n      return true;\n    }\n    if (this.map[node.name]) {\n      this.parser = this.map[node.name];\n      this.parser.parseOpen(node);\n      return true;\n    }\n    return false;\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n      return true;\n    }\n    return false;\n  }\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.parser = undefined;\n      }\n      return true;\n    }\n    if (this.map.tabColor.model || this.map.pageSetUpPr.model || this.map.outlinePr.model) {\n      this.model = {};\n      if (this.map.tabColor.model) {\n        this.model.tabColor = this.map.tabColor.model;\n      }\n      if (this.map.pageSetUpPr.model) {\n        this.model.pageSetup = this.map.pageSetUpPr.model;\n      }\n      if (this.map.outlinePr.model) {\n        this.model.outlineProperties = this.map.outlinePr.model;\n      }\n    } else {\n      this.model = null;\n    }\n    return false;\n  }\n}\n\nmodule.exports = SheetPropertiesXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC1C,MAAMC,UAAU,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AAClD,MAAME,wBAAwB,GAAGF,OAAO,CAAC,+BAA+B,CAAC;AACzE,MAAMG,sBAAsB,GAAGH,OAAO,CAAC,4BAA4B,CAAC;AAEpE,MAAMI,oBAAoB,SAASL,SAAS,CAAC;EAC3CM,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACTC,QAAQ,EAAE,IAAIN,UAAU,CAAC,UAAU,CAAC;MACpCO,WAAW,EAAE,IAAIN,wBAAwB,CAAC,CAAC;MAC3CO,SAAS,EAAE,IAAIN,sBAAsB,CAAC;IACxC,CAAC;EACH;EAEA,IAAIO,GAAGA,CAAA,EAAG;IACR,OAAO,SAAS;EAClB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvB,IAAIA,KAAK,EAAE;MACTD,SAAS,CAACE,WAAW,CAAC,CAAC;MACvBF,SAAS,CAACG,QAAQ,CAAC,SAAS,CAAC;MAE7B,IAAIC,KAAK,GAAG,KAAK;MACjBA,KAAK,GAAG,IAAI,CAACV,GAAG,CAACC,QAAQ,CAACI,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACN,QAAQ,CAAC,IAAIS,KAAK;MACpEA,KAAK,GAAG,IAAI,CAACV,GAAG,CAACE,WAAW,CAACG,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACI,SAAS,CAAC,IAAID,KAAK;MACxEA,KAAK,GAAG,IAAI,CAACV,GAAG,CAACG,SAAS,CAACE,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACK,iBAAiB,CAAC,IAAIF,KAAK;MAE9E,IAAIA,KAAK,EAAE;QACTJ,SAAS,CAACO,SAAS,CAAC,CAAC;QACrBP,SAAS,CAACQ,MAAM,CAAC,CAAC;MACpB,CAAC,MAAM;QACLR,SAAS,CAACS,QAAQ,CAAC,CAAC;MACtB;IACF;EACF;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,IAAIA,IAAI,CAACE,IAAI,KAAK,IAAI,CAACf,GAAG,EAAE;MAC1B,IAAI,CAACgB,KAAK,CAAC,CAAC;MACZ,OAAO,IAAI;IACb;IACA,IAAI,IAAI,CAACpB,GAAG,CAACiB,IAAI,CAACE,IAAI,CAAC,EAAE;MACvB,IAAI,CAACD,MAAM,GAAG,IAAI,CAAClB,GAAG,CAACiB,IAAI,CAACE,IAAI,CAAC;MACjC,IAAI,CAACD,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAI,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACJ,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACG,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAC,UAAUA,CAACJ,IAAI,EAAE;IACf,IAAI,IAAI,CAACD,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACK,UAAU,CAACJ,IAAI,CAAC,EAAE;QACjC,IAAI,CAACD,MAAM,GAAGM,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,IAAI,IAAI,CAACxB,GAAG,CAACC,QAAQ,CAACM,KAAK,IAAI,IAAI,CAACP,GAAG,CAACE,WAAW,CAACK,KAAK,IAAI,IAAI,CAACP,GAAG,CAACG,SAAS,CAACI,KAAK,EAAE;MACrF,IAAI,CAACA,KAAK,GAAG,CAAC,CAAC;MACf,IAAI,IAAI,CAACP,GAAG,CAACC,QAAQ,CAACM,KAAK,EAAE;QAC3B,IAAI,CAACA,KAAK,CAACN,QAAQ,GAAG,IAAI,CAACD,GAAG,CAACC,QAAQ,CAACM,KAAK;MAC/C;MACA,IAAI,IAAI,CAACP,GAAG,CAACE,WAAW,CAACK,KAAK,EAAE;QAC9B,IAAI,CAACA,KAAK,CAACI,SAAS,GAAG,IAAI,CAACX,GAAG,CAACE,WAAW,CAACK,KAAK;MACnD;MACA,IAAI,IAAI,CAACP,GAAG,CAACG,SAAS,CAACI,KAAK,EAAE;QAC5B,IAAI,CAACA,KAAK,CAACK,iBAAiB,GAAG,IAAI,CAACZ,GAAG,CAACG,SAAS,CAACI,KAAK;MACzD;IACF,CAAC,MAAM;MACL,IAAI,CAACA,KAAK,GAAG,IAAI;IACnB;IACA,OAAO,KAAK;EACd;AACF;AAEAkB,MAAM,CAACC,OAAO,GAAG5B,oBAAoB"}