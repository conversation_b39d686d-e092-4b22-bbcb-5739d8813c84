{"version": 3, "file": "vml-anchor-xform.js", "names": ["BaseXform", "require", "VmlAnchorXform", "tag", "getAnchorRect", "anchor", "l", "Math", "floor", "left", "lf", "t", "top", "tf", "r", "right", "rf", "b", "bottom", "bf", "getDefaultRect", "ref", "col", "max", "row", "render", "xmlStream", "model", "rect", "refAddress", "leafNode", "join", "parseOpen", "node", "name", "text", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/comment/vml-anchor-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\n// render the triangle in the cell for the comment\nclass VmlAnchorXform extends BaseXform {\n  get tag() {\n    return 'x:Anchor';\n  }\n\n  getAnchorRect(anchor) {\n    const l = Math.floor(anchor.left);\n    const lf = Math.floor((anchor.left - l) * 68);\n    const t = Math.floor(anchor.top);\n    const tf = Math.floor((anchor.top - t) * 18);\n    const r = Math.floor(anchor.right);\n    const rf = Math.floor((anchor.right - r) * 68);\n    const b = Math.floor(anchor.bottom);\n    const bf = Math.floor((anchor.bottom - b) * 18);\n    return [l, lf, t, tf, r, rf, b, bf];\n  }\n\n  getDefaultRect(ref) {\n    const l = ref.col;\n    const lf = 6;\n    const t = Math.max(ref.row - 2, 0);\n    const tf = 14;\n    const r = l + 2;\n    const rf = 2;\n    const b = t + 4;\n    const bf = 16;\n    return [l, lf, t, tf, r, rf, b, bf];\n  }\n\n  render(xmlStream, model) {\n    const rect = model.anchor\n      ? this.getAnchorRect(model.anchor)\n      : this.getDefaultRect(model.refAddress);\n\n    xmlStream.leafNode('x:Anchor', null, rect.join(', '));\n  }\n\n  parseOpen(node) {\n    switch (node.name) {\n      case this.tag:\n        this.text = '';\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  parseText(text) {\n    this.text = text;\n  }\n\n  parseClose() {\n    return false;\n  }\n}\n\nmodule.exports = VmlAnchorXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;;AAE1C;AACA,MAAMC,cAAc,SAASF,SAAS,CAAC;EACrC,IAAIG,GAAGA,CAAA,EAAG;IACR,OAAO,UAAU;EACnB;EAEAC,aAAaA,CAACC,MAAM,EAAE;IACpB,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACH,MAAM,CAACI,IAAI,CAAC;IACjC,MAAMC,EAAE,GAAGH,IAAI,CAACC,KAAK,CAAC,CAACH,MAAM,CAACI,IAAI,GAAGH,CAAC,IAAI,EAAE,CAAC;IAC7C,MAAMK,CAAC,GAAGJ,IAAI,CAACC,KAAK,CAACH,MAAM,CAACO,GAAG,CAAC;IAChC,MAAMC,EAAE,GAAGN,IAAI,CAACC,KAAK,CAAC,CAACH,MAAM,CAACO,GAAG,GAAGD,CAAC,IAAI,EAAE,CAAC;IAC5C,MAAMG,CAAC,GAAGP,IAAI,CAACC,KAAK,CAACH,MAAM,CAACU,KAAK,CAAC;IAClC,MAAMC,EAAE,GAAGT,IAAI,CAACC,KAAK,CAAC,CAACH,MAAM,CAACU,KAAK,GAAGD,CAAC,IAAI,EAAE,CAAC;IAC9C,MAAMG,CAAC,GAAGV,IAAI,CAACC,KAAK,CAACH,MAAM,CAACa,MAAM,CAAC;IACnC,MAAMC,EAAE,GAAGZ,IAAI,CAACC,KAAK,CAAC,CAACH,MAAM,CAACa,MAAM,GAAGD,CAAC,IAAI,EAAE,CAAC;IAC/C,OAAO,CAACX,CAAC,EAAEI,EAAE,EAAEC,CAAC,EAAEE,EAAE,EAAEC,CAAC,EAAEE,EAAE,EAAEC,CAAC,EAAEE,EAAE,CAAC;EACrC;EAEAC,cAAcA,CAACC,GAAG,EAAE;IAClB,MAAMf,CAAC,GAAGe,GAAG,CAACC,GAAG;IACjB,MAAMZ,EAAE,GAAG,CAAC;IACZ,MAAMC,CAAC,GAAGJ,IAAI,CAACgB,GAAG,CAACF,GAAG,CAACG,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;IAClC,MAAMX,EAAE,GAAG,EAAE;IACb,MAAMC,CAAC,GAAGR,CAAC,GAAG,CAAC;IACf,MAAMU,EAAE,GAAG,CAAC;IACZ,MAAMC,CAAC,GAAGN,CAAC,GAAG,CAAC;IACf,MAAMQ,EAAE,GAAG,EAAE;IACb,OAAO,CAACb,CAAC,EAAEI,EAAE,EAAEC,CAAC,EAAEE,EAAE,EAAEC,CAAC,EAAEE,EAAE,EAAEC,CAAC,EAAEE,EAAE,CAAC;EACrC;EAEAM,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvB,MAAMC,IAAI,GAAGD,KAAK,CAACtB,MAAM,GACrB,IAAI,CAACD,aAAa,CAACuB,KAAK,CAACtB,MAAM,CAAC,GAChC,IAAI,CAACe,cAAc,CAACO,KAAK,CAACE,UAAU,CAAC;IAEzCH,SAAS,CAACI,QAAQ,CAAC,UAAU,EAAE,IAAI,EAAEF,IAAI,CAACG,IAAI,CAAC,IAAI,CAAC,CAAC;EACvD;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,QAAQA,IAAI,CAACC,IAAI;MACf,KAAK,IAAI,CAAC/B,GAAG;QACX,IAAI,CAACgC,IAAI,GAAG,EAAE;QACd,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAC,SAASA,CAACD,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;EAClB;EAEAE,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGrC,cAAc"}