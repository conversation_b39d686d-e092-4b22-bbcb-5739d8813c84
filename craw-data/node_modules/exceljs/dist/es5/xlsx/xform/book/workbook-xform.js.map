{"version": 3, "file": "workbook-xform.js", "names": ["_", "require", "co<PERSON><PERSON><PERSON>", "XmlStream", "BaseXform", "StaticXform", "ListXform", "DefinedNameXform", "SheetXform", "WorkbookViewXform", "WorkbookPropertiesXform", "WorkbookCalcPropertiesXform", "WorkbookXform", "constructor", "map", "fileVersion", "STATIC_XFORMS", "workbookPr", "bookViews", "tag", "count", "childXform", "sheets", "definedNames", "calcPr", "prepare", "model", "worksheets", "printAreas", "index", "for<PERSON>ach", "sheet", "pageSetup", "printArea", "split", "printAreaComponents", "definedName", "name", "ranges", "localSheetId", "push", "printTitlesRow", "printTitlesColumn", "titlesColumns", "titlesRows", "length", "concat", "media", "medium", "i", "type", "render", "xmlStream", "openXml", "StdDocAttributes", "openNode", "WORKBOOK_ATTRIBUTES", "properties", "views", "calcProperties", "closeNode", "parseOpen", "node", "parser", "parseText", "text", "parseClose", "undefined", "reconcile", "rels", "workbookRels", "reduce", "rel", "Id", "worksheet", "rId", "worksheetHash", "Target", "replace", "id", "state", "each", "range", "decodeEx", "dimensions", "rangeString", "join", "dollarRegex", "rowRangeRegex", "rowRangeMatches", "match", "columnRangeRegex", "columnRangeMatches", "xmlns", "$", "appName", "lastEdited", "lowestEdited", "rupBuild", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/book/workbook-xform.js"], "sourcesContent": ["const _ = require('../../../utils/under-dash');\n\nconst colCache = require('../../../utils/col-cache');\nconst XmlStream = require('../../../utils/xml-stream');\n\nconst BaseXform = require('../base-xform');\nconst StaticXform = require('../static-xform');\nconst ListXform = require('../list-xform');\nconst DefinedNameXform = require('./defined-name-xform');\nconst SheetXform = require('./sheet-xform');\nconst WorkbookViewXform = require('./workbook-view-xform');\nconst WorkbookPropertiesXform = require('./workbook-properties-xform');\nconst WorkbookCalcPropertiesXform = require('./workbook-calc-properties-xform');\n\nclass WorkbookXform extends BaseXform {\n  constructor() {\n    super();\n\n    this.map = {\n      fileVersion: WorkbookXform.STATIC_XFORMS.fileVersion,\n      workbookPr: new WorkbookPropertiesXform(),\n      bookViews: new ListXform({\n        tag: 'bookViews',\n        count: false,\n        childXform: new WorkbookViewXform(),\n      }),\n      sheets: new ListXform({tag: 'sheets', count: false, childXform: new SheetXform()}),\n      definedNames: new ListXform({\n        tag: 'definedNames',\n        count: false,\n        childXform: new DefinedNameXform(),\n      }),\n      calcPr: new WorkbookCalcPropertiesXform(),\n    };\n  }\n\n  prepare(model) {\n    model.sheets = model.worksheets;\n\n    // collate all the print areas from all of the sheets and add them to the defined names\n    const printAreas = [];\n    let index = 0; // sheets is sparse array - calc index manually\n    model.sheets.forEach(sheet => {\n      if (sheet.pageSetup && sheet.pageSetup.printArea) {\n        sheet.pageSetup.printArea.split('&&').forEach(printArea => {\n          const printAreaComponents = printArea.split(':');\n          const definedName = {\n            name: '_xlnm.Print_Area',\n            ranges: [`'${sheet.name}'!$${printAreaComponents[0]}:$${printAreaComponents[1]}`],\n            localSheetId: index,\n          };\n          printAreas.push(definedName);\n        });\n      }\n\n      if (\n        sheet.pageSetup &&\n        (sheet.pageSetup.printTitlesRow || sheet.pageSetup.printTitlesColumn)\n      ) {\n        const ranges = [];\n\n        if (sheet.pageSetup.printTitlesColumn) {\n          const titlesColumns = sheet.pageSetup.printTitlesColumn.split(':');\n          ranges.push(`'${sheet.name}'!$${titlesColumns[0]}:$${titlesColumns[1]}`);\n        }\n\n        if (sheet.pageSetup.printTitlesRow) {\n          const titlesRows = sheet.pageSetup.printTitlesRow.split(':');\n          ranges.push(`'${sheet.name}'!$${titlesRows[0]}:$${titlesRows[1]}`);\n        }\n\n        const definedName = {\n          name: '_xlnm.Print_Titles',\n          ranges,\n          localSheetId: index,\n        };\n\n        printAreas.push(definedName);\n      }\n      index++;\n    });\n    if (printAreas.length) {\n      model.definedNames = model.definedNames.concat(printAreas);\n    }\n\n    (model.media || []).forEach((medium, i) => {\n      // assign name\n      medium.name = medium.type + (i + 1);\n    });\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openXml(XmlStream.StdDocAttributes);\n    xmlStream.openNode('workbook', WorkbookXform.WORKBOOK_ATTRIBUTES);\n\n    this.map.fileVersion.render(xmlStream);\n    this.map.workbookPr.render(xmlStream, model.properties);\n    this.map.bookViews.render(xmlStream, model.views);\n    this.map.sheets.render(xmlStream, model.sheets);\n    this.map.definedNames.render(xmlStream, model.definedNames);\n    this.map.calcPr.render(xmlStream, model.calcProperties);\n\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    switch (node.name) {\n      case 'workbook':\n        return true;\n      default:\n        this.parser = this.map[node.name];\n        if (this.parser) {\n          this.parser.parseOpen(node);\n        }\n        return true;\n    }\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.parser = undefined;\n      }\n      return true;\n    }\n    switch (name) {\n      case 'workbook':\n        this.model = {\n          sheets: this.map.sheets.model,\n          properties: this.map.workbookPr.model || {},\n          views: this.map.bookViews.model,\n          calcProperties: {},\n        };\n        if (this.map.definedNames.model) {\n          this.model.definedNames = this.map.definedNames.model;\n        }\n\n        return false;\n      default:\n        // not quite sure how we get here!\n        return true;\n    }\n  }\n\n  reconcile(model) {\n    const rels = (model.workbookRels || []).reduce((map, rel) => {\n      map[rel.Id] = rel;\n      return map;\n    }, {});\n\n    // reconcile sheet ids, rIds and names\n    const worksheets = [];\n    let worksheet;\n    let index = 0;\n\n    (model.sheets || []).forEach(sheet => {\n      const rel = rels[sheet.rId];\n      if (!rel) {\n        return;\n      }\n      // if rel.Target start with `[space]/xl/` or `/xl/` , then it will be replaced with `''` and spliced behind `xl/`,\n      // otherwise it will be spliced directly behind `xl/`. i.g.\n      worksheet = model.worksheetHash[`xl/${rel.Target.replace(/^(\\s|\\/xl\\/)+/, '')}`];\n      // If there are \"chartsheets\" in the file, rel.Target will\n      // come out as chartsheets/sheet1.xml or similar here, and\n      // that won't be in model.worksheetHash.\n      // As we don't have the infrastructure to support chartsheets,\n      // we will ignore them for now:\n      if (worksheet) {\n        worksheet.name = sheet.name;\n        worksheet.id = sheet.id;\n        worksheet.state = sheet.state;\n        worksheets[index++] = worksheet;\n      }\n    });\n\n    // reconcile print areas\n    const definedNames = [];\n    _.each(model.definedNames, definedName => {\n      if (definedName.name === '_xlnm.Print_Area') {\n        worksheet = worksheets[definedName.localSheetId];\n        if (worksheet) {\n          if (!worksheet.pageSetup) {\n            worksheet.pageSetup = {};\n          }\n          const range = colCache.decodeEx(definedName.ranges[0]);\n          worksheet.pageSetup.printArea = worksheet.pageSetup.printArea\n            ? `${worksheet.pageSetup.printArea}&&${range.dimensions}`\n            : range.dimensions;\n        }\n      } else if (definedName.name === '_xlnm.Print_Titles') {\n        worksheet = worksheets[definedName.localSheetId];\n        if (worksheet) {\n          if (!worksheet.pageSetup) {\n            worksheet.pageSetup = {};\n          }\n\n          const rangeString = definedName.ranges.join(',');\n\n          const dollarRegex = /\\$/g;\n\n          const rowRangeRegex = /\\$\\d+:\\$\\d+/;\n          const rowRangeMatches = rangeString.match(rowRangeRegex);\n\n          if (rowRangeMatches && rowRangeMatches.length) {\n            const range = rowRangeMatches[0];\n            worksheet.pageSetup.printTitlesRow = range.replace(dollarRegex, '');\n          }\n\n          const columnRangeRegex = /\\$[A-Z]+:\\$[A-Z]+/;\n          const columnRangeMatches = rangeString.match(columnRangeRegex);\n\n          if (columnRangeMatches && columnRangeMatches.length) {\n            const range = columnRangeMatches[0];\n            worksheet.pageSetup.printTitlesColumn = range.replace(dollarRegex, '');\n          }\n        }\n      } else {\n        definedNames.push(definedName);\n      }\n    });\n    model.definedNames = definedNames;\n\n    // used by sheets to build their image models\n    model.media.forEach((media, i) => {\n      media.index = i;\n    });\n  }\n}\n\nWorkbookXform.WORKBOOK_ATTRIBUTES = {\n  xmlns: 'http://schemas.openxmlformats.org/spreadsheetml/2006/main',\n  'xmlns:r': 'http://schemas.openxmlformats.org/officeDocument/2006/relationships',\n  'xmlns:mc': 'http://schemas.openxmlformats.org/markup-compatibility/2006',\n  'mc:Ignorable': 'x15',\n  'xmlns:x15': 'http://schemas.microsoft.com/office/spreadsheetml/2010/11/main',\n};\nWorkbookXform.STATIC_XFORMS = {\n  fileVersion: new StaticXform({\n    tag: 'fileVersion',\n    $: {appName: 'xl', lastEdited: 5, lowestEdited: 5, rupBuild: 9303},\n  }),\n};\n\nmodule.exports = WorkbookXform;\n"], "mappings": ";;AAAA,MAAMA,CAAC,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AAE9C,MAAMC,QAAQ,GAAGD,OAAO,CAAC,0BAA0B,CAAC;AACpD,MAAME,SAAS,GAAGF,OAAO,CAAC,2BAA2B,CAAC;AAEtD,MAAMG,SAAS,GAAGH,OAAO,CAAC,eAAe,CAAC;AAC1C,MAAMI,WAAW,GAAGJ,OAAO,CAAC,iBAAiB,CAAC;AAC9C,MAAMK,SAAS,GAAGL,OAAO,CAAC,eAAe,CAAC;AAC1C,MAAMM,gBAAgB,GAAGN,OAAO,CAAC,sBAAsB,CAAC;AACxD,MAAMO,UAAU,GAAGP,OAAO,CAAC,eAAe,CAAC;AAC3C,MAAMQ,iBAAiB,GAAGR,OAAO,CAAC,uBAAuB,CAAC;AAC1D,MAAMS,uBAAuB,GAAGT,OAAO,CAAC,6BAA6B,CAAC;AACtE,MAAMU,2BAA2B,GAAGV,OAAO,CAAC,kCAAkC,CAAC;AAE/E,MAAMW,aAAa,SAASR,SAAS,CAAC;EACpCS,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACTC,WAAW,EAAEH,aAAa,CAACI,aAAa,CAACD,WAAW;MACpDE,UAAU,EAAE,IAAIP,uBAAuB,CAAC,CAAC;MACzCQ,SAAS,EAAE,IAAIZ,SAAS,CAAC;QACvBa,GAAG,EAAE,WAAW;QAChBC,KAAK,EAAE,KAAK;QACZC,UAAU,EAAE,IAAIZ,iBAAiB,CAAC;MACpC,CAAC,CAAC;MACFa,MAAM,EAAE,IAAIhB,SAAS,CAAC;QAACa,GAAG,EAAE,QAAQ;QAAEC,KAAK,EAAE,KAAK;QAAEC,UAAU,EAAE,IAAIb,UAAU,CAAC;MAAC,CAAC,CAAC;MAClFe,YAAY,EAAE,IAAIjB,SAAS,CAAC;QAC1Ba,GAAG,EAAE,cAAc;QACnBC,KAAK,EAAE,KAAK;QACZC,UAAU,EAAE,IAAId,gBAAgB,CAAC;MACnC,CAAC,CAAC;MACFiB,MAAM,EAAE,IAAIb,2BAA2B,CAAC;IAC1C,CAAC;EACH;EAEAc,OAAOA,CAACC,KAAK,EAAE;IACbA,KAAK,CAACJ,MAAM,GAAGI,KAAK,CAACC,UAAU;;IAE/B;IACA,MAAMC,UAAU,GAAG,EAAE;IACrB,IAAIC,KAAK,GAAG,CAAC,CAAC,CAAC;IACfH,KAAK,CAACJ,MAAM,CAACQ,OAAO,CAACC,KAAK,IAAI;MAC5B,IAAIA,KAAK,CAACC,SAAS,IAAID,KAAK,CAACC,SAAS,CAACC,SAAS,EAAE;QAChDF,KAAK,CAACC,SAAS,CAACC,SAAS,CAACC,KAAK,CAAC,IAAI,CAAC,CAACJ,OAAO,CAACG,SAAS,IAAI;UACzD,MAAME,mBAAmB,GAAGF,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC;UAChD,MAAME,WAAW,GAAG;YAClBC,IAAI,EAAE,kBAAkB;YACxBC,MAAM,EAAE,CAAE,IAAGP,KAAK,CAACM,IAAK,MAAKF,mBAAmB,CAAC,CAAC,CAAE,KAAIA,mBAAmB,CAAC,CAAC,CAAE,EAAC,CAAC;YACjFI,YAAY,EAAEV;UAChB,CAAC;UACDD,UAAU,CAACY,IAAI,CAACJ,WAAW,CAAC;QAC9B,CAAC,CAAC;MACJ;MAEA,IACEL,KAAK,CAACC,SAAS,KACdD,KAAK,CAACC,SAAS,CAACS,cAAc,IAAIV,KAAK,CAACC,SAAS,CAACU,iBAAiB,CAAC,EACrE;QACA,MAAMJ,MAAM,GAAG,EAAE;QAEjB,IAAIP,KAAK,CAACC,SAAS,CAACU,iBAAiB,EAAE;UACrC,MAAMC,aAAa,GAAGZ,KAAK,CAACC,SAAS,CAACU,iBAAiB,CAACR,KAAK,CAAC,GAAG,CAAC;UAClEI,MAAM,CAACE,IAAI,CAAE,IAAGT,KAAK,CAACM,IAAK,MAAKM,aAAa,CAAC,CAAC,CAAE,KAAIA,aAAa,CAAC,CAAC,CAAE,EAAC,CAAC;QAC1E;QAEA,IAAIZ,KAAK,CAACC,SAAS,CAACS,cAAc,EAAE;UAClC,MAAMG,UAAU,GAAGb,KAAK,CAACC,SAAS,CAACS,cAAc,CAACP,KAAK,CAAC,GAAG,CAAC;UAC5DI,MAAM,CAACE,IAAI,CAAE,IAAGT,KAAK,CAACM,IAAK,MAAKO,UAAU,CAAC,CAAC,CAAE,KAAIA,UAAU,CAAC,CAAC,CAAE,EAAC,CAAC;QACpE;QAEA,MAAMR,WAAW,GAAG;UAClBC,IAAI,EAAE,oBAAoB;UAC1BC,MAAM;UACNC,YAAY,EAAEV;QAChB,CAAC;QAEDD,UAAU,CAACY,IAAI,CAACJ,WAAW,CAAC;MAC9B;MACAP,KAAK,EAAE;IACT,CAAC,CAAC;IACF,IAAID,UAAU,CAACiB,MAAM,EAAE;MACrBnB,KAAK,CAACH,YAAY,GAAGG,KAAK,CAACH,YAAY,CAACuB,MAAM,CAAClB,UAAU,CAAC;IAC5D;IAEA,CAACF,KAAK,CAACqB,KAAK,IAAI,EAAE,EAAEjB,OAAO,CAAC,CAACkB,MAAM,EAAEC,CAAC,KAAK;MACzC;MACAD,MAAM,CAACX,IAAI,GAAGW,MAAM,CAACE,IAAI,IAAID,CAAC,GAAG,CAAC,CAAC;IACrC,CAAC,CAAC;EACJ;EAEAE,MAAMA,CAACC,SAAS,EAAE1B,KAAK,EAAE;IACvB0B,SAAS,CAACC,OAAO,CAAClD,SAAS,CAACmD,gBAAgB,CAAC;IAC7CF,SAAS,CAACG,QAAQ,CAAC,UAAU,EAAE3C,aAAa,CAAC4C,mBAAmB,CAAC;IAEjE,IAAI,CAAC1C,GAAG,CAACC,WAAW,CAACoC,MAAM,CAACC,SAAS,CAAC;IACtC,IAAI,CAACtC,GAAG,CAACG,UAAU,CAACkC,MAAM,CAACC,SAAS,EAAE1B,KAAK,CAAC+B,UAAU,CAAC;IACvD,IAAI,CAAC3C,GAAG,CAACI,SAAS,CAACiC,MAAM,CAACC,SAAS,EAAE1B,KAAK,CAACgC,KAAK,CAAC;IACjD,IAAI,CAAC5C,GAAG,CAACQ,MAAM,CAAC6B,MAAM,CAACC,SAAS,EAAE1B,KAAK,CAACJ,MAAM,CAAC;IAC/C,IAAI,CAACR,GAAG,CAACS,YAAY,CAAC4B,MAAM,CAACC,SAAS,EAAE1B,KAAK,CAACH,YAAY,CAAC;IAC3D,IAAI,CAACT,GAAG,CAACU,MAAM,CAAC2B,MAAM,CAACC,SAAS,EAAE1B,KAAK,CAACiC,cAAc,CAAC;IAEvDP,SAAS,CAACQ,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,QAAQA,IAAI,CAACzB,IAAI;MACf,KAAK,UAAU;QACb,OAAO,IAAI;MACb;QACE,IAAI,CAAC0B,MAAM,GAAG,IAAI,CAACjD,GAAG,CAACgD,IAAI,CAACzB,IAAI,CAAC;QACjC,IAAI,IAAI,CAAC0B,MAAM,EAAE;UACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;QAC7B;QACA,OAAO,IAAI;IACf;EACF;EAEAE,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACF,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACC,SAAS,CAACC,IAAI,CAAC;IAC7B;EACF;EAEAC,UAAUA,CAAC7B,IAAI,EAAE;IACf,IAAI,IAAI,CAAC0B,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACG,UAAU,CAAC7B,IAAI,CAAC,EAAE;QACjC,IAAI,CAAC0B,MAAM,GAAGI,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,QAAQ9B,IAAI;MACV,KAAK,UAAU;QACb,IAAI,CAACX,KAAK,GAAG;UACXJ,MAAM,EAAE,IAAI,CAACR,GAAG,CAACQ,MAAM,CAACI,KAAK;UAC7B+B,UAAU,EAAE,IAAI,CAAC3C,GAAG,CAACG,UAAU,CAACS,KAAK,IAAI,CAAC,CAAC;UAC3CgC,KAAK,EAAE,IAAI,CAAC5C,GAAG,CAACI,SAAS,CAACQ,KAAK;UAC/BiC,cAAc,EAAE,CAAC;QACnB,CAAC;QACD,IAAI,IAAI,CAAC7C,GAAG,CAACS,YAAY,CAACG,KAAK,EAAE;UAC/B,IAAI,CAACA,KAAK,CAACH,YAAY,GAAG,IAAI,CAACT,GAAG,CAACS,YAAY,CAACG,KAAK;QACvD;QAEA,OAAO,KAAK;MACd;QACE;QACA,OAAO,IAAI;IACf;EACF;EAEA0C,SAASA,CAAC1C,KAAK,EAAE;IACf,MAAM2C,IAAI,GAAG,CAAC3C,KAAK,CAAC4C,YAAY,IAAI,EAAE,EAAEC,MAAM,CAAC,CAACzD,GAAG,EAAE0D,GAAG,KAAK;MAC3D1D,GAAG,CAAC0D,GAAG,CAACC,EAAE,CAAC,GAAGD,GAAG;MACjB,OAAO1D,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEN;IACA,MAAMa,UAAU,GAAG,EAAE;IACrB,IAAI+C,SAAS;IACb,IAAI7C,KAAK,GAAG,CAAC;IAEb,CAACH,KAAK,CAACJ,MAAM,IAAI,EAAE,EAAEQ,OAAO,CAACC,KAAK,IAAI;MACpC,MAAMyC,GAAG,GAAGH,IAAI,CAACtC,KAAK,CAAC4C,GAAG,CAAC;MAC3B,IAAI,CAACH,GAAG,EAAE;QACR;MACF;MACA;MACA;MACAE,SAAS,GAAGhD,KAAK,CAACkD,aAAa,CAAE,MAAKJ,GAAG,CAACK,MAAM,CAACC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAE,EAAC,CAAC;MAChF;MACA;MACA;MACA;MACA;MACA,IAAIJ,SAAS,EAAE;QACbA,SAAS,CAACrC,IAAI,GAAGN,KAAK,CAACM,IAAI;QAC3BqC,SAAS,CAACK,EAAE,GAAGhD,KAAK,CAACgD,EAAE;QACvBL,SAAS,CAACM,KAAK,GAAGjD,KAAK,CAACiD,KAAK;QAC7BrD,UAAU,CAACE,KAAK,EAAE,CAAC,GAAG6C,SAAS;MACjC;IACF,CAAC,CAAC;;IAEF;IACA,MAAMnD,YAAY,GAAG,EAAE;IACvBvB,CAAC,CAACiF,IAAI,CAACvD,KAAK,CAACH,YAAY,EAAEa,WAAW,IAAI;MACxC,IAAIA,WAAW,CAACC,IAAI,KAAK,kBAAkB,EAAE;QAC3CqC,SAAS,GAAG/C,UAAU,CAACS,WAAW,CAACG,YAAY,CAAC;QAChD,IAAImC,SAAS,EAAE;UACb,IAAI,CAACA,SAAS,CAAC1C,SAAS,EAAE;YACxB0C,SAAS,CAAC1C,SAAS,GAAG,CAAC,CAAC;UAC1B;UACA,MAAMkD,KAAK,GAAGhF,QAAQ,CAACiF,QAAQ,CAAC/C,WAAW,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC;UACtDoC,SAAS,CAAC1C,SAAS,CAACC,SAAS,GAAGyC,SAAS,CAAC1C,SAAS,CAACC,SAAS,GACxD,GAAEyC,SAAS,CAAC1C,SAAS,CAACC,SAAU,KAAIiD,KAAK,CAACE,UAAW,EAAC,GACvDF,KAAK,CAACE,UAAU;QACtB;MACF,CAAC,MAAM,IAAIhD,WAAW,CAACC,IAAI,KAAK,oBAAoB,EAAE;QACpDqC,SAAS,GAAG/C,UAAU,CAACS,WAAW,CAACG,YAAY,CAAC;QAChD,IAAImC,SAAS,EAAE;UACb,IAAI,CAACA,SAAS,CAAC1C,SAAS,EAAE;YACxB0C,SAAS,CAAC1C,SAAS,GAAG,CAAC,CAAC;UAC1B;UAEA,MAAMqD,WAAW,GAAGjD,WAAW,CAACE,MAAM,CAACgD,IAAI,CAAC,GAAG,CAAC;UAEhD,MAAMC,WAAW,GAAG,KAAK;UAEzB,MAAMC,aAAa,GAAG,aAAa;UACnC,MAAMC,eAAe,GAAGJ,WAAW,CAACK,KAAK,CAACF,aAAa,CAAC;UAExD,IAAIC,eAAe,IAAIA,eAAe,CAAC5C,MAAM,EAAE;YAC7C,MAAMqC,KAAK,GAAGO,eAAe,CAAC,CAAC,CAAC;YAChCf,SAAS,CAAC1C,SAAS,CAACS,cAAc,GAAGyC,KAAK,CAACJ,OAAO,CAACS,WAAW,EAAE,EAAE,CAAC;UACrE;UAEA,MAAMI,gBAAgB,GAAG,mBAAmB;UAC5C,MAAMC,kBAAkB,GAAGP,WAAW,CAACK,KAAK,CAACC,gBAAgB,CAAC;UAE9D,IAAIC,kBAAkB,IAAIA,kBAAkB,CAAC/C,MAAM,EAAE;YACnD,MAAMqC,KAAK,GAAGU,kBAAkB,CAAC,CAAC,CAAC;YACnClB,SAAS,CAAC1C,SAAS,CAACU,iBAAiB,GAAGwC,KAAK,CAACJ,OAAO,CAACS,WAAW,EAAE,EAAE,CAAC;UACxE;QACF;MACF,CAAC,MAAM;QACLhE,YAAY,CAACiB,IAAI,CAACJ,WAAW,CAAC;MAChC;IACF,CAAC,CAAC;IACFV,KAAK,CAACH,YAAY,GAAGA,YAAY;;IAEjC;IACAG,KAAK,CAACqB,KAAK,CAACjB,OAAO,CAAC,CAACiB,KAAK,EAAEE,CAAC,KAAK;MAChCF,KAAK,CAAClB,KAAK,GAAGoB,CAAC;IACjB,CAAC,CAAC;EACJ;AACF;AAEArC,aAAa,CAAC4C,mBAAmB,GAAG;EAClCqC,KAAK,EAAE,2DAA2D;EAClE,SAAS,EAAE,qEAAqE;EAChF,UAAU,EAAE,6DAA6D;EACzE,cAAc,EAAE,KAAK;EACrB,WAAW,EAAE;AACf,CAAC;AACDjF,aAAa,CAACI,aAAa,GAAG;EAC5BD,WAAW,EAAE,IAAIV,WAAW,CAAC;IAC3Bc,GAAG,EAAE,aAAa;IAClB2E,CAAC,EAAE;MAACC,OAAO,EAAE,IAAI;MAAEC,UAAU,EAAE,CAAC;MAAEC,YAAY,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAI;EACnE,CAAC;AACH,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAGxF,aAAa"}