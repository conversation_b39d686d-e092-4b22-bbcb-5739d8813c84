{"version": 3, "file": "fill-xform.js", "names": ["BaseXform", "require", "ColorXform", "StopXform", "constructor", "map", "color", "tag", "render", "xmlStream", "model", "openNode", "addAttribute", "position", "closeNode", "parseOpen", "node", "parser", "name", "parseFloat", "attributes", "parseText", "parseClose", "undefined", "PatternFillXform", "fgColor", "bgColor", "pattern", "type", "patternType", "text", "GradientFillXform", "stop", "gradient", "degree", "center", "left", "right", "top", "bottom", "stopXform", "stops", "for<PERSON>ach", "stopModel", "parseInt", "push", "FillXform", "patternFill", "gradientFill", "add<PERSON><PERSON><PERSON>", "rollback", "commit", "validStyle", "value", "validPatternV<PERSON>ues", "reduce", "p", "v", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/style/fill-xform.js"], "sourcesContent": ["/* eslint-disable max-classes-per-file */\nconst BaseXform = require('../base-xform');\n\nconst ColorXform = require('./color-xform');\n\nclass StopXform extends BaseXform {\n  constructor() {\n    super();\n\n    this.map = {\n      color: new ColorXform(),\n    };\n  }\n\n  get tag() {\n    return 'stop';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openNode('stop');\n    xmlStream.addAttribute('position', model.position);\n    this.map.color.render(xmlStream, model.color);\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    switch (node.name) {\n      case 'stop':\n        this.model = {\n          position: parseFloat(node.attributes.position),\n        };\n        return true;\n      case 'color':\n        this.parser = this.map.color;\n        this.parser.parseOpen(node);\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  parseText() {}\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.model.color = this.parser.model;\n        this.parser = undefined;\n      }\n      return true;\n    }\n    return false;\n  }\n}\n\nclass PatternFillXform extends BaseXform {\n  constructor() {\n    super();\n\n    this.map = {\n      fgColor: new ColorXform('fgColor'),\n      bgColor: new ColorXform('bgColor'),\n    };\n  }\n\n  get name() {\n    return 'pattern';\n  }\n\n  get tag() {\n    return 'patternFill';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openNode('patternFill');\n    xmlStream.addAttribute('patternType', model.pattern);\n    if (model.fgColor) {\n      this.map.fgColor.render(xmlStream, model.fgColor);\n    }\n    if (model.bgColor) {\n      this.map.bgColor.render(xmlStream, model.bgColor);\n    }\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    switch (node.name) {\n      case 'patternFill':\n        this.model = {\n          type: 'pattern',\n          pattern: node.attributes.patternType,\n        };\n        return true;\n      default:\n        this.parser = this.map[node.name];\n        if (this.parser) {\n          this.parser.parseOpen(node);\n          return true;\n        }\n        return false;\n    }\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        if (this.parser.model) {\n          this.model[name] = this.parser.model;\n        }\n        this.parser = undefined;\n      }\n      return true;\n    }\n    return false;\n  }\n}\n\nclass GradientFillXform extends BaseXform {\n  constructor() {\n    super();\n\n    this.map = {\n      stop: new StopXform(),\n    };\n    // if (model) {\n    //   this.gradient = model.gradient;\n    //   if (model.center) {\n    //     this.center = model.center;\n    //   }\n    //   if (model.degree !== undefined) {\n    //     this.degree = model.degree;\n    //   }\n    //   this.stops = model.stops.map(function(stop) { return new StopXform(stop); });\n    // } else {\n    //   this.stops = [];\n    // }\n  }\n\n  get name() {\n    return 'gradient';\n  }\n\n  get tag() {\n    return 'gradientFill';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openNode('gradientFill');\n    switch (model.gradient) {\n      case 'angle':\n        xmlStream.addAttribute('degree', model.degree);\n        break;\n      case 'path':\n        xmlStream.addAttribute('type', 'path');\n        if (model.center.left) {\n          xmlStream.addAttribute('left', model.center.left);\n          if (model.center.right === undefined) {\n            xmlStream.addAttribute('right', model.center.left);\n          }\n        }\n        if (model.center.right) {\n          xmlStream.addAttribute('right', model.center.right);\n        }\n        if (model.center.top) {\n          xmlStream.addAttribute('top', model.center.top);\n          if (model.center.bottom === undefined) {\n            xmlStream.addAttribute('bottom', model.center.top);\n          }\n        }\n        if (model.center.bottom) {\n          xmlStream.addAttribute('bottom', model.center.bottom);\n        }\n        break;\n\n      default:\n        break;\n    }\n\n    const stopXform = this.map.stop;\n    model.stops.forEach(stopModel => {\n      stopXform.render(xmlStream, stopModel);\n    });\n\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    switch (node.name) {\n      case 'gradientFill': {\n        const model = (this.model = {\n          stops: [],\n        });\n        if (node.attributes.degree) {\n          model.gradient = 'angle';\n          model.degree = parseInt(node.attributes.degree, 10);\n        } else if (node.attributes.type === 'path') {\n          model.gradient = 'path';\n          model.center = {\n            left: node.attributes.left ? parseFloat(node.attributes.left) : 0,\n            top: node.attributes.top ? parseFloat(node.attributes.top) : 0,\n          };\n          if (node.attributes.right !== node.attributes.left) {\n            model.center.right = node.attributes.right ? parseFloat(node.attributes.right) : 0;\n          }\n          if (node.attributes.bottom !== node.attributes.top) {\n            model.center.bottom = node.attributes.bottom ? parseFloat(node.attributes.bottom) : 0;\n          }\n        }\n        return true;\n      }\n\n      case 'stop':\n        this.parser = this.map.stop;\n        this.parser.parseOpen(node);\n        return true;\n\n      default:\n        return false;\n    }\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.model.stops.push(this.parser.model);\n        this.parser = undefined;\n      }\n      return true;\n    }\n    return false;\n  }\n}\n\n// Fill encapsulates translation from fill model to/from xlsx\nclass FillXform extends BaseXform {\n  constructor() {\n    super();\n\n    this.map = {\n      patternFill: new PatternFillXform(),\n      gradientFill: new GradientFillXform(),\n    };\n  }\n\n  get tag() {\n    return 'fill';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.addRollback();\n    xmlStream.openNode('fill');\n    switch (model.type) {\n      case 'pattern':\n        this.map.patternFill.render(xmlStream, model);\n        break;\n      case 'gradient':\n        this.map.gradientFill.render(xmlStream, model);\n        break;\n      default:\n        xmlStream.rollback();\n        return;\n    }\n    xmlStream.closeNode();\n    xmlStream.commit();\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    switch (node.name) {\n      case 'fill':\n        this.model = {};\n        return true;\n      default:\n        this.parser = this.map[node.name];\n        if (this.parser) {\n          this.parser.parseOpen(node);\n          return true;\n        }\n        return false;\n    }\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.model = this.parser.model;\n        this.model.type = this.parser.name;\n        this.parser = undefined;\n      }\n      return true;\n    }\n    return false;\n  }\n\n  validStyle(value) {\n    return FillXform.validPatternValues[value];\n  }\n}\n\nFillXform.validPatternValues = [\n  'none',\n  'solid',\n  'darkVertical',\n  'darkGray',\n  'mediumGray',\n  'lightGray',\n  'gray125',\n  'gray0625',\n  'darkHorizontal',\n  'darkVertical',\n  'darkDown',\n  'darkUp',\n  'darkGrid',\n  'darkTrellis',\n  'lightHorizontal',\n  'lightVertical',\n  'lightDown',\n  'lightUp',\n  'lightGrid',\n  'lightTrellis',\n  'lightGrid',\n].reduce((p, v) => {\n  p[v] = true;\n  return p;\n}, {});\n\nFillXform.StopXform = StopXform;\nFillXform.PatternFillXform = PatternFillXform;\nFillXform.GradientFillXform = GradientFillXform;\n\nmodule.exports = FillXform;\n"], "mappings": ";;AAAA;AACA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,UAAU,GAAGD,OAAO,CAAC,eAAe,CAAC;AAE3C,MAAME,SAAS,SAASH,SAAS,CAAC;EAChCI,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACTC,KAAK,EAAE,IAAIJ,UAAU,CAAC;IACxB,CAAC;EACH;EAEA,IAAIK,GAAGA,CAAA,EAAG;IACR,OAAO,MAAM;EACf;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,MAAM,CAAC;IAC1BF,SAAS,CAACG,YAAY,CAAC,UAAU,EAAEF,KAAK,CAACG,QAAQ,CAAC;IAClD,IAAI,CAACR,GAAG,CAACC,KAAK,CAACE,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACJ,KAAK,CAAC;IAC7CG,SAAS,CAACK,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,QAAQA,IAAI,CAACE,IAAI;MACf,KAAK,MAAM;QACT,IAAI,CAACR,KAAK,GAAG;UACXG,QAAQ,EAAEM,UAAU,CAACH,IAAI,CAACI,UAAU,CAACP,QAAQ;QAC/C,CAAC;QACD,OAAO,IAAI;MACb,KAAK,OAAO;QACV,IAAI,CAACI,MAAM,GAAG,IAAI,CAACZ,GAAG,CAACC,KAAK;QAC5B,IAAI,CAACW,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;QAC3B,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAK,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAACJ,IAAI,EAAE;IACf,IAAI,IAAI,CAACD,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACK,UAAU,CAACJ,IAAI,CAAC,EAAE;QACjC,IAAI,CAACR,KAAK,CAACJ,KAAK,GAAG,IAAI,CAACW,MAAM,CAACP,KAAK;QACpC,IAAI,CAACO,MAAM,GAAGM,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;AACF;AAEA,MAAMC,gBAAgB,SAASxB,SAAS,CAAC;EACvCI,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACToB,OAAO,EAAE,IAAIvB,UAAU,CAAC,SAAS,CAAC;MAClCwB,OAAO,EAAE,IAAIxB,UAAU,CAAC,SAAS;IACnC,CAAC;EACH;EAEA,IAAIgB,IAAIA,CAAA,EAAG;IACT,OAAO,SAAS;EAClB;EAEA,IAAIX,GAAGA,CAAA,EAAG;IACR,OAAO,aAAa;EACtB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,aAAa,CAAC;IACjCF,SAAS,CAACG,YAAY,CAAC,aAAa,EAAEF,KAAK,CAACiB,OAAO,CAAC;IACpD,IAAIjB,KAAK,CAACe,OAAO,EAAE;MACjB,IAAI,CAACpB,GAAG,CAACoB,OAAO,CAACjB,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACe,OAAO,CAAC;IACnD;IACA,IAAIf,KAAK,CAACgB,OAAO,EAAE;MACjB,IAAI,CAACrB,GAAG,CAACqB,OAAO,CAAClB,MAAM,CAACC,SAAS,EAAEC,KAAK,CAACgB,OAAO,CAAC;IACnD;IACAjB,SAAS,CAACK,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,QAAQA,IAAI,CAACE,IAAI;MACf,KAAK,aAAa;QAChB,IAAI,CAACR,KAAK,GAAG;UACXkB,IAAI,EAAE,SAAS;UACfD,OAAO,EAAEX,IAAI,CAACI,UAAU,CAACS;QAC3B,CAAC;QACD,OAAO,IAAI;MACb;QACE,IAAI,CAACZ,MAAM,GAAG,IAAI,CAACZ,GAAG,CAACW,IAAI,CAACE,IAAI,CAAC;QACjC,IAAI,IAAI,CAACD,MAAM,EAAE;UACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;UAC3B,OAAO,IAAI;QACb;QACA,OAAO,KAAK;IAChB;EACF;EAEAK,SAASA,CAACS,IAAI,EAAE;IACd,IAAI,IAAI,CAACb,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACI,SAAS,CAACS,IAAI,CAAC;IAC7B;EACF;EAEAR,UAAUA,CAACJ,IAAI,EAAE;IACf,IAAI,IAAI,CAACD,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACK,UAAU,CAACJ,IAAI,CAAC,EAAE;QACjC,IAAI,IAAI,CAACD,MAAM,CAACP,KAAK,EAAE;UACrB,IAAI,CAACA,KAAK,CAACQ,IAAI,CAAC,GAAG,IAAI,CAACD,MAAM,CAACP,KAAK;QACtC;QACA,IAAI,CAACO,MAAM,GAAGM,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;AACF;AAEA,MAAMQ,iBAAiB,SAAS/B,SAAS,CAAC;EACxCI,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACT2B,IAAI,EAAE,IAAI7B,SAAS,CAAC;IACtB,CAAC;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;;EAEA,IAAIe,IAAIA,CAAA,EAAG;IACT,OAAO,UAAU;EACnB;EAEA,IAAIX,GAAGA,CAAA,EAAG;IACR,OAAO,cAAc;EACvB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,cAAc,CAAC;IAClC,QAAQD,KAAK,CAACuB,QAAQ;MACpB,KAAK,OAAO;QACVxB,SAAS,CAACG,YAAY,CAAC,QAAQ,EAAEF,KAAK,CAACwB,MAAM,CAAC;QAC9C;MACF,KAAK,MAAM;QACTzB,SAAS,CAACG,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC;QACtC,IAAIF,KAAK,CAACyB,MAAM,CAACC,IAAI,EAAE;UACrB3B,SAAS,CAACG,YAAY,CAAC,MAAM,EAAEF,KAAK,CAACyB,MAAM,CAACC,IAAI,CAAC;UACjD,IAAI1B,KAAK,CAACyB,MAAM,CAACE,KAAK,KAAKd,SAAS,EAAE;YACpCd,SAAS,CAACG,YAAY,CAAC,OAAO,EAAEF,KAAK,CAACyB,MAAM,CAACC,IAAI,CAAC;UACpD;QACF;QACA,IAAI1B,KAAK,CAACyB,MAAM,CAACE,KAAK,EAAE;UACtB5B,SAAS,CAACG,YAAY,CAAC,OAAO,EAAEF,KAAK,CAACyB,MAAM,CAACE,KAAK,CAAC;QACrD;QACA,IAAI3B,KAAK,CAACyB,MAAM,CAACG,GAAG,EAAE;UACpB7B,SAAS,CAACG,YAAY,CAAC,KAAK,EAAEF,KAAK,CAACyB,MAAM,CAACG,GAAG,CAAC;UAC/C,IAAI5B,KAAK,CAACyB,MAAM,CAACI,MAAM,KAAKhB,SAAS,EAAE;YACrCd,SAAS,CAACG,YAAY,CAAC,QAAQ,EAAEF,KAAK,CAACyB,MAAM,CAACG,GAAG,CAAC;UACpD;QACF;QACA,IAAI5B,KAAK,CAACyB,MAAM,CAACI,MAAM,EAAE;UACvB9B,SAAS,CAACG,YAAY,CAAC,QAAQ,EAAEF,KAAK,CAACyB,MAAM,CAACI,MAAM,CAAC;QACvD;QACA;MAEF;QACE;IACJ;IAEA,MAAMC,SAAS,GAAG,IAAI,CAACnC,GAAG,CAAC2B,IAAI;IAC/BtB,KAAK,CAAC+B,KAAK,CAACC,OAAO,CAACC,SAAS,IAAI;MAC/BH,SAAS,CAAChC,MAAM,CAACC,SAAS,EAAEkC,SAAS,CAAC;IACxC,CAAC,CAAC;IAEFlC,SAAS,CAACK,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,QAAQA,IAAI,CAACE,IAAI;MACf,KAAK,cAAc;QAAE;UACnB,MAAMR,KAAK,GAAI,IAAI,CAACA,KAAK,GAAG;YAC1B+B,KAAK,EAAE;UACT,CAAE;UACF,IAAIzB,IAAI,CAACI,UAAU,CAACc,MAAM,EAAE;YAC1BxB,KAAK,CAACuB,QAAQ,GAAG,OAAO;YACxBvB,KAAK,CAACwB,MAAM,GAAGU,QAAQ,CAAC5B,IAAI,CAACI,UAAU,CAACc,MAAM,EAAE,EAAE,CAAC;UACrD,CAAC,MAAM,IAAIlB,IAAI,CAACI,UAAU,CAACQ,IAAI,KAAK,MAAM,EAAE;YAC1ClB,KAAK,CAACuB,QAAQ,GAAG,MAAM;YACvBvB,KAAK,CAACyB,MAAM,GAAG;cACbC,IAAI,EAAEpB,IAAI,CAACI,UAAU,CAACgB,IAAI,GAAGjB,UAAU,CAACH,IAAI,CAACI,UAAU,CAACgB,IAAI,CAAC,GAAG,CAAC;cACjEE,GAAG,EAAEtB,IAAI,CAACI,UAAU,CAACkB,GAAG,GAAGnB,UAAU,CAACH,IAAI,CAACI,UAAU,CAACkB,GAAG,CAAC,GAAG;YAC/D,CAAC;YACD,IAAItB,IAAI,CAACI,UAAU,CAACiB,KAAK,KAAKrB,IAAI,CAACI,UAAU,CAACgB,IAAI,EAAE;cAClD1B,KAAK,CAACyB,MAAM,CAACE,KAAK,GAAGrB,IAAI,CAACI,UAAU,CAACiB,KAAK,GAAGlB,UAAU,CAACH,IAAI,CAACI,UAAU,CAACiB,KAAK,CAAC,GAAG,CAAC;YACpF;YACA,IAAIrB,IAAI,CAACI,UAAU,CAACmB,MAAM,KAAKvB,IAAI,CAACI,UAAU,CAACkB,GAAG,EAAE;cAClD5B,KAAK,CAACyB,MAAM,CAACI,MAAM,GAAGvB,IAAI,CAACI,UAAU,CAACmB,MAAM,GAAGpB,UAAU,CAACH,IAAI,CAACI,UAAU,CAACmB,MAAM,CAAC,GAAG,CAAC;YACvF;UACF;UACA,OAAO,IAAI;QACb;MAEA,KAAK,MAAM;QACT,IAAI,CAACtB,MAAM,GAAG,IAAI,CAACZ,GAAG,CAAC2B,IAAI;QAC3B,IAAI,CAACf,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;QAC3B,OAAO,IAAI;MAEb;QACE,OAAO,KAAK;IAChB;EACF;EAEAK,SAASA,CAACS,IAAI,EAAE;IACd,IAAI,IAAI,CAACb,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACI,SAAS,CAACS,IAAI,CAAC;IAC7B;EACF;EAEAR,UAAUA,CAACJ,IAAI,EAAE;IACf,IAAI,IAAI,CAACD,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACK,UAAU,CAACJ,IAAI,CAAC,EAAE;QACjC,IAAI,CAACR,KAAK,CAAC+B,KAAK,CAACI,IAAI,CAAC,IAAI,CAAC5B,MAAM,CAACP,KAAK,CAAC;QACxC,IAAI,CAACO,MAAM,GAAGM,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;AACF;;AAEA;AACA,MAAMuB,SAAS,SAAS9C,SAAS,CAAC;EAChCI,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACT0C,WAAW,EAAE,IAAIvB,gBAAgB,CAAC,CAAC;MACnCwB,YAAY,EAAE,IAAIjB,iBAAiB,CAAC;IACtC,CAAC;EACH;EAEA,IAAIxB,GAAGA,CAAA,EAAG;IACR,OAAO,MAAM;EACf;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACwC,WAAW,CAAC,CAAC;IACvBxC,SAAS,CAACE,QAAQ,CAAC,MAAM,CAAC;IAC1B,QAAQD,KAAK,CAACkB,IAAI;MAChB,KAAK,SAAS;QACZ,IAAI,CAACvB,GAAG,CAAC0C,WAAW,CAACvC,MAAM,CAACC,SAAS,EAAEC,KAAK,CAAC;QAC7C;MACF,KAAK,UAAU;QACb,IAAI,CAACL,GAAG,CAAC2C,YAAY,CAACxC,MAAM,CAACC,SAAS,EAAEC,KAAK,CAAC;QAC9C;MACF;QACED,SAAS,CAACyC,QAAQ,CAAC,CAAC;QACpB;IACJ;IACAzC,SAAS,CAACK,SAAS,CAAC,CAAC;IACrBL,SAAS,CAAC0C,MAAM,CAAC,CAAC;EACpB;EAEApC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,QAAQA,IAAI,CAACE,IAAI;MACf,KAAK,MAAM;QACT,IAAI,CAACR,KAAK,GAAG,CAAC,CAAC;QACf,OAAO,IAAI;MACb;QACE,IAAI,CAACO,MAAM,GAAG,IAAI,CAACZ,GAAG,CAACW,IAAI,CAACE,IAAI,CAAC;QACjC,IAAI,IAAI,CAACD,MAAM,EAAE;UACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;UAC3B,OAAO,IAAI;QACb;QACA,OAAO,KAAK;IAChB;EACF;EAEAK,SAASA,CAACS,IAAI,EAAE;IACd,IAAI,IAAI,CAACb,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACI,SAAS,CAACS,IAAI,CAAC;IAC7B;EACF;EAEAR,UAAUA,CAACJ,IAAI,EAAE;IACf,IAAI,IAAI,CAACD,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACK,UAAU,CAACJ,IAAI,CAAC,EAAE;QACjC,IAAI,CAACR,KAAK,GAAG,IAAI,CAACO,MAAM,CAACP,KAAK;QAC9B,IAAI,CAACA,KAAK,CAACkB,IAAI,GAAG,IAAI,CAACX,MAAM,CAACC,IAAI;QAClC,IAAI,CAACD,MAAM,GAAGM,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEA6B,UAAUA,CAACC,KAAK,EAAE;IAChB,OAAOP,SAAS,CAACQ,kBAAkB,CAACD,KAAK,CAAC;EAC5C;AACF;AAEAP,SAAS,CAACQ,kBAAkB,GAAG,CAC7B,MAAM,EACN,OAAO,EACP,cAAc,EACd,UAAU,EACV,YAAY,EACZ,WAAW,EACX,SAAS,EACT,UAAU,EACV,gBAAgB,EAChB,cAAc,EACd,UAAU,EACV,QAAQ,EACR,UAAU,EACV,aAAa,EACb,iBAAiB,EACjB,eAAe,EACf,WAAW,EACX,SAAS,EACT,WAAW,EACX,cAAc,EACd,WAAW,CACZ,CAACC,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;EACjBD,CAAC,CAACC,CAAC,CAAC,GAAG,IAAI;EACX,OAAOD,CAAC;AACV,CAAC,EAAE,CAAC,CAAC,CAAC;AAENV,SAAS,CAAC3C,SAAS,GAAGA,SAAS;AAC/B2C,SAAS,CAACtB,gBAAgB,GAAGA,gBAAgB;AAC7CsB,SAAS,CAACf,iBAAiB,GAAGA,iBAAiB;AAE/C2B,MAAM,CAACC,OAAO,GAAGb,SAAS"}