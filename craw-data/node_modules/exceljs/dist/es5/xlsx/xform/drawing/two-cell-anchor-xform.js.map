{"version": 3, "file": "two-cell-anchor-xform.js", "names": ["BaseCellAnchorXform", "require", "StaticXform", "CellPositionXform", "PicXform", "TwoCellAnchorXform", "constructor", "map", "tag", "prepare", "model", "options", "picture", "render", "xmlStream", "openNode", "editAs", "range", "tl", "br", "closeNode", "parseClose", "name", "parser", "undefined", "reconcile", "medium", "reconcilePicture", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/drawing/two-cell-anchor-xform.js"], "sourcesContent": ["const BaseCellAnchorXform = require('./base-cell-anchor-xform');\nconst StaticXform = require('../static-xform');\n\nconst CellPositionXform = require('./cell-position-xform');\nconst PicXform = require('./pic-xform');\n\nclass TwoCellAnchorXform extends BaseCellAnchorXform {\n  constructor() {\n    super();\n\n    this.map = {\n      'xdr:from': new CellPositionXform({tag: 'xdr:from'}),\n      'xdr:to': new CellPositionXform({tag: 'xdr:to'}),\n      'xdr:pic': new PicXform(),\n      'xdr:clientData': new StaticXform({tag: 'xdr:clientData'}),\n    };\n  }\n\n  get tag() {\n    return 'xdr:twoCellAnchor';\n  }\n\n  prepare(model, options) {\n    this.map['xdr:pic'].prepare(model.picture, options);\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openNode(this.tag, {editAs: model.range.editAs || 'oneCell'});\n\n    this.map['xdr:from'].render(xmlStream, model.range.tl);\n    this.map['xdr:to'].render(xmlStream, model.range.br);\n    this.map['xdr:pic'].render(xmlStream, model.picture);\n    this.map['xdr:clientData'].render(xmlStream, {});\n\n    xmlStream.closeNode();\n  }\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.parser = undefined;\n      }\n      return true;\n    }\n    switch (name) {\n      case this.tag:\n        this.model.range.tl = this.map['xdr:from'].model;\n        this.model.range.br = this.map['xdr:to'].model;\n        this.model.picture = this.map['xdr:pic'].model;\n        return false;\n      default:\n        // could be some unrecognised tags\n        return true;\n    }\n  }\n\n  reconcile(model, options) {\n    model.medium = this.reconcilePicture(model.picture, options);\n  }\n}\n\nmodule.exports = TwoCellAnchorXform;\n"], "mappings": ";;AAAA,MAAMA,mBAAmB,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AAC/D,MAAMC,WAAW,GAAGD,OAAO,CAAC,iBAAiB,CAAC;AAE9C,MAAME,iBAAiB,GAAGF,OAAO,CAAC,uBAAuB,CAAC;AAC1D,MAAMG,QAAQ,GAAGH,OAAO,CAAC,aAAa,CAAC;AAEvC,MAAMI,kBAAkB,SAASL,mBAAmB,CAAC;EACnDM,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACT,UAAU,EAAE,IAAIJ,iBAAiB,CAAC;QAACK,GAAG,EAAE;MAAU,CAAC,CAAC;MACpD,QAAQ,EAAE,IAAIL,iBAAiB,CAAC;QAACK,GAAG,EAAE;MAAQ,CAAC,CAAC;MAChD,SAAS,EAAE,IAAIJ,QAAQ,CAAC,CAAC;MACzB,gBAAgB,EAAE,IAAIF,WAAW,CAAC;QAACM,GAAG,EAAE;MAAgB,CAAC;IAC3D,CAAC;EACH;EAEA,IAAIA,GAAGA,CAAA,EAAG;IACR,OAAO,mBAAmB;EAC5B;EAEAC,OAAOA,CAACC,KAAK,EAAEC,OAAO,EAAE;IACtB,IAAI,CAACJ,GAAG,CAAC,SAAS,CAAC,CAACE,OAAO,CAACC,KAAK,CAACE,OAAO,EAAED,OAAO,CAAC;EACrD;EAEAE,MAAMA,CAACC,SAAS,EAAEJ,KAAK,EAAE;IACvBI,SAAS,CAACC,QAAQ,CAAC,IAAI,CAACP,GAAG,EAAE;MAACQ,MAAM,EAAEN,KAAK,CAACO,KAAK,CAACD,MAAM,IAAI;IAAS,CAAC,CAAC;IAEvE,IAAI,CAACT,GAAG,CAAC,UAAU,CAAC,CAACM,MAAM,CAACC,SAAS,EAAEJ,KAAK,CAACO,KAAK,CAACC,EAAE,CAAC;IACtD,IAAI,CAACX,GAAG,CAAC,QAAQ,CAAC,CAACM,MAAM,CAACC,SAAS,EAAEJ,KAAK,CAACO,KAAK,CAACE,EAAE,CAAC;IACpD,IAAI,CAACZ,GAAG,CAAC,SAAS,CAAC,CAACM,MAAM,CAACC,SAAS,EAAEJ,KAAK,CAACE,OAAO,CAAC;IACpD,IAAI,CAACL,GAAG,CAAC,gBAAgB,CAAC,CAACM,MAAM,CAACC,SAAS,EAAE,CAAC,CAAC,CAAC;IAEhDA,SAAS,CAACM,SAAS,CAAC,CAAC;EACvB;EAEAC,UAAUA,CAACC,IAAI,EAAE;IACf,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACF,UAAU,CAACC,IAAI,CAAC,EAAE;QACjC,IAAI,CAACC,MAAM,GAAGC,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,QAAQF,IAAI;MACV,KAAK,IAAI,CAACd,GAAG;QACX,IAAI,CAACE,KAAK,CAACO,KAAK,CAACC,EAAE,GAAG,IAAI,CAACX,GAAG,CAAC,UAAU,CAAC,CAACG,KAAK;QAChD,IAAI,CAACA,KAAK,CAACO,KAAK,CAACE,EAAE,GAAG,IAAI,CAACZ,GAAG,CAAC,QAAQ,CAAC,CAACG,KAAK;QAC9C,IAAI,CAACA,KAAK,CAACE,OAAO,GAAG,IAAI,CAACL,GAAG,CAAC,SAAS,CAAC,CAACG,KAAK;QAC9C,OAAO,KAAK;MACd;QACE;QACA,OAAO,IAAI;IACf;EACF;EAEAe,SAASA,CAACf,KAAK,EAAEC,OAAO,EAAE;IACxBD,KAAK,CAACgB,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACjB,KAAK,CAACE,OAAO,EAAED,OAAO,CAAC;EAC9D;AACF;AAEAiB,MAAM,CAACC,OAAO,GAAGxB,kBAAkB"}