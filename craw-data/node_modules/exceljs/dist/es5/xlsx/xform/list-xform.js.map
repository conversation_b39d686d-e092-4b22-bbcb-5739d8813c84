{"version": 3, "file": "list-xform.js", "names": ["BaseXform", "require", "ListXform", "constructor", "options", "tag", "always", "count", "empty", "$count", "$", "childXform", "maxItems", "prepare", "model", "for<PERSON>ach", "childModel", "index", "render", "xmlStream", "length", "openNode", "addAttribute", "closeNode", "leafNode", "parseOpen", "node", "parser", "name", "parseText", "text", "parseClose", "push", "undefined", "Error", "reconcile", "module", "exports"], "sources": ["../../../../lib/xlsx/xform/list-xform.js"], "sourcesContent": ["const BaseXform = require('./base-xform');\n\nclass ListXform extends BaseXform {\n  constructor(options) {\n    super();\n\n    this.tag = options.tag;\n    this.always = !!options.always;\n    this.count = options.count;\n    this.empty = options.empty;\n    this.$count = options.$count || 'count';\n    this.$ = options.$;\n    this.childXform = options.childXform;\n    this.maxItems = options.maxItems;\n  }\n\n  prepare(model, options) {\n    const {childXform} = this;\n    if (model) {\n      model.forEach((childModel, index) => {\n        options.index = index;\n        childXform.prepare(childModel, options);\n      });\n    }\n  }\n\n  render(xmlStream, model) {\n    if (this.always || (model && model.length)) {\n      xmlStream.openNode(this.tag, this.$);\n      if (this.count) {\n        xmlStream.addAttribute(this.$count, (model && model.length) || 0);\n      }\n\n      const {childXform} = this;\n      (model || []).forEach((childModel, index) => {\n        childXform.render(xmlStream, childModel, index);\n      });\n\n      xmlStream.closeNode();\n    } else if (this.empty) {\n      xmlStream.leafNode(this.tag);\n    }\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    switch (node.name) {\n      case this.tag:\n        this.model = [];\n        return true;\n      default:\n        if (this.childXform.parseOpen(node)) {\n          this.parser = this.childXform;\n          return true;\n        }\n        return false;\n    }\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.model.push(this.parser.model);\n        this.parser = undefined;\n\n        if (this.maxItems && this.model.length > this.maxItems) {\n          throw new Error(`Max ${this.childXform.tag} count (${this.maxItems}) exceeded`);\n        }\n      }\n      return true;\n    }\n\n    return false;\n  }\n\n  reconcile(model, options) {\n    if (model) {\n      const {childXform} = this;\n      model.forEach(childModel => {\n        childXform.reconcile(childModel, options);\n      });\n    }\n  }\n}\n\nmodule.exports = ListXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,cAAc,CAAC;AAEzC,MAAMC,SAAS,SAASF,SAAS,CAAC;EAChCG,WAAWA,CAACC,OAAO,EAAE;IACnB,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAGD,OAAO,CAACC,GAAG;IACtB,IAAI,CAACC,MAAM,GAAG,CAAC,CAACF,OAAO,CAACE,MAAM;IAC9B,IAAI,CAACC,KAAK,GAAGH,OAAO,CAACG,KAAK;IAC1B,IAAI,CAACC,KAAK,GAAGJ,OAAO,CAACI,KAAK;IAC1B,IAAI,CAACC,MAAM,GAAGL,OAAO,CAACK,MAAM,IAAI,OAAO;IACvC,IAAI,CAACC,CAAC,GAAGN,OAAO,CAACM,CAAC;IAClB,IAAI,CAACC,UAAU,GAAGP,OAAO,CAACO,UAAU;IACpC,IAAI,CAACC,QAAQ,GAAGR,OAAO,CAACQ,QAAQ;EAClC;EAEAC,OAAOA,CAACC,KAAK,EAAEV,OAAO,EAAE;IACtB,MAAM;MAACO;IAAU,CAAC,GAAG,IAAI;IACzB,IAAIG,KAAK,EAAE;MACTA,KAAK,CAACC,OAAO,CAAC,CAACC,UAAU,EAAEC,KAAK,KAAK;QACnCb,OAAO,CAACa,KAAK,GAAGA,KAAK;QACrBN,UAAU,CAACE,OAAO,CAACG,UAAU,EAAEZ,OAAO,CAAC;MACzC,CAAC,CAAC;IACJ;EACF;EAEAc,MAAMA,CAACC,SAAS,EAAEL,KAAK,EAAE;IACvB,IAAI,IAAI,CAACR,MAAM,IAAKQ,KAAK,IAAIA,KAAK,CAACM,MAAO,EAAE;MAC1CD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAAChB,GAAG,EAAE,IAAI,CAACK,CAAC,CAAC;MACpC,IAAI,IAAI,CAACH,KAAK,EAAE;QACdY,SAAS,CAACG,YAAY,CAAC,IAAI,CAACb,MAAM,EAAGK,KAAK,IAAIA,KAAK,CAACM,MAAM,IAAK,CAAC,CAAC;MACnE;MAEA,MAAM;QAACT;MAAU,CAAC,GAAG,IAAI;MACzB,CAACG,KAAK,IAAI,EAAE,EAAEC,OAAO,CAAC,CAACC,UAAU,EAAEC,KAAK,KAAK;QAC3CN,UAAU,CAACO,MAAM,CAACC,SAAS,EAAEH,UAAU,EAAEC,KAAK,CAAC;MACjD,CAAC,CAAC;MAEFE,SAAS,CAACI,SAAS,CAAC,CAAC;IACvB,CAAC,MAAM,IAAI,IAAI,CAACf,KAAK,EAAE;MACrBW,SAAS,CAACK,QAAQ,CAAC,IAAI,CAACnB,GAAG,CAAC;IAC9B;EACF;EAEAoB,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,QAAQA,IAAI,CAACE,IAAI;MACf,KAAK,IAAI,CAACvB,GAAG;QACX,IAAI,CAACS,KAAK,GAAG,EAAE;QACf,OAAO,IAAI;MACb;QACE,IAAI,IAAI,CAACH,UAAU,CAACc,SAAS,CAACC,IAAI,CAAC,EAAE;UACnC,IAAI,CAACC,MAAM,GAAG,IAAI,CAAChB,UAAU;UAC7B,OAAO,IAAI;QACb;QACA,OAAO,KAAK;IAChB;EACF;EAEAkB,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACH,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACE,SAAS,CAACC,IAAI,CAAC;IAC7B;EACF;EAEAC,UAAUA,CAACH,IAAI,EAAE;IACf,IAAI,IAAI,CAACD,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACI,UAAU,CAACH,IAAI,CAAC,EAAE;QACjC,IAAI,CAACd,KAAK,CAACkB,IAAI,CAAC,IAAI,CAACL,MAAM,CAACb,KAAK,CAAC;QAClC,IAAI,CAACa,MAAM,GAAGM,SAAS;QAEvB,IAAI,IAAI,CAACrB,QAAQ,IAAI,IAAI,CAACE,KAAK,CAACM,MAAM,GAAG,IAAI,CAACR,QAAQ,EAAE;UACtD,MAAM,IAAIsB,KAAK,CAAE,OAAM,IAAI,CAACvB,UAAU,CAACN,GAAI,WAAU,IAAI,CAACO,QAAS,YAAW,CAAC;QACjF;MACF;MACA,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd;EAEAuB,SAASA,CAACrB,KAAK,EAAEV,OAAO,EAAE;IACxB,IAAIU,KAAK,EAAE;MACT,MAAM;QAACH;MAAU,CAAC,GAAG,IAAI;MACzBG,KAAK,CAACC,OAAO,CAACC,UAAU,IAAI;QAC1BL,UAAU,CAACwB,SAAS,CAACnB,UAAU,EAAEZ,OAAO,CAAC;MAC3C,CAAC,CAAC;IACJ;EACF;AACF;AAEAgC,MAAM,CAACC,OAAO,GAAGnC,SAAS"}