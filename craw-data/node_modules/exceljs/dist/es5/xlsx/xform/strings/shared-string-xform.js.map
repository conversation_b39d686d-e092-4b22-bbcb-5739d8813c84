{"version": 3, "file": "shared-string-xform.js", "names": ["TextXform", "require", "RichTextXform", "PhoneticTextXform", "BaseXform", "SharedStringXform", "constructor", "model", "map", "r", "t", "rPh", "tag", "render", "xmlStream", "openNode", "hasOwnProperty", "richText", "length", "for<PERSON>ach", "text", "undefined", "closeNode", "parseOpen", "node", "name", "parser", "parseText", "parseClose", "rt", "push", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/strings/shared-string-xform.js"], "sourcesContent": ["const TextXform = require('./text-xform');\nconst RichTextXform = require('./rich-text-xform');\nconst PhoneticTextXform = require('./phonetic-text-xform');\n\nconst BaseXform = require('../base-xform');\n\n// <si>\n//   <r></r><r></r>...\n// </si>\n// <si>\n//   <t></t>\n// </si>\n\nclass SharedStringXform extends BaseXform {\n  constructor(model) {\n    super();\n\n    this.model = model;\n\n    this.map = {\n      r: new RichTextXform(),\n      t: new TextXform(),\n      rPh: new PhoneticTextXform(),\n    };\n  }\n\n  get tag() {\n    return 'si';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openNode(this.tag);\n    if (model && model.hasOwnProperty('richText') && model.richText) {\n      if (model.richText.length) {\n        model.richText.forEach(text => {\n          this.map.r.render(xmlStream, text);\n        });\n      } else {\n        this.map.t.render(xmlStream, '');\n      }\n    } else if (model !== undefined && model !== null) {\n      this.map.t.render(xmlStream, model);\n    }\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    const {name} = node;\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    if (name === this.tag) {\n      this.model = {};\n      return true;\n    }\n    this.parser = this.map[name];\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    return false;\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        switch (name) {\n          case 'r': {\n            let rt = this.model.richText;\n            if (!rt) {\n              rt = this.model.richText = [];\n            }\n            rt.push(this.parser.model);\n            break;\n          }\n          case 't':\n            this.model = this.parser.model;\n            break;\n          default:\n            break;\n        }\n        this.parser = undefined;\n      }\n      return true;\n    }\n    switch (name) {\n      case this.tag:\n        return false;\n      default:\n        return true;\n    }\n  }\n}\n\nmodule.exports = SharedStringXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,cAAc,CAAC;AACzC,MAAMC,aAAa,GAAGD,OAAO,CAAC,mBAAmB,CAAC;AAClD,MAAME,iBAAiB,GAAGF,OAAO,CAAC,uBAAuB,CAAC;AAE1D,MAAMG,SAAS,GAAGH,OAAO,CAAC,eAAe,CAAC;;AAE1C;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMI,iBAAiB,SAASD,SAAS,CAAC;EACxCE,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAAC,CAAC;IAEP,IAAI,CAACA,KAAK,GAAGA,KAAK;IAElB,IAAI,CAACC,GAAG,GAAG;MACTC,CAAC,EAAE,IAAIP,aAAa,CAAC,CAAC;MACtBQ,CAAC,EAAE,IAAIV,SAAS,CAAC,CAAC;MAClBW,GAAG,EAAE,IAAIR,iBAAiB,CAAC;IAC7B,CAAC;EACH;EAEA,IAAIS,GAAGA,CAAA,EAAG;IACR,OAAO,IAAI;EACb;EAEAC,MAAMA,CAACC,SAAS,EAAEP,KAAK,EAAE;IACvBO,SAAS,CAACC,QAAQ,CAAC,IAAI,CAACH,GAAG,CAAC;IAC5B,IAAIL,KAAK,IAAIA,KAAK,CAACS,cAAc,CAAC,UAAU,CAAC,IAAIT,KAAK,CAACU,QAAQ,EAAE;MAC/D,IAAIV,KAAK,CAACU,QAAQ,CAACC,MAAM,EAAE;QACzBX,KAAK,CAACU,QAAQ,CAACE,OAAO,CAACC,IAAI,IAAI;UAC7B,IAAI,CAACZ,GAAG,CAACC,CAAC,CAACI,MAAM,CAACC,SAAS,EAAEM,IAAI,CAAC;QACpC,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAACZ,GAAG,CAACE,CAAC,CAACG,MAAM,CAACC,SAAS,EAAE,EAAE,CAAC;MAClC;IACF,CAAC,MAAM,IAAIP,KAAK,KAAKc,SAAS,IAAId,KAAK,KAAK,IAAI,EAAE;MAChD,IAAI,CAACC,GAAG,CAACE,CAAC,CAACG,MAAM,CAACC,SAAS,EAAEP,KAAK,CAAC;IACrC;IACAO,SAAS,CAACQ,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,MAAM;MAACC;IAAI,CAAC,GAAGD,IAAI;IACnB,IAAI,IAAI,CAACE,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACH,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,IAAIC,IAAI,KAAK,IAAI,CAACb,GAAG,EAAE;MACrB,IAAI,CAACL,KAAK,GAAG,CAAC,CAAC;MACf,OAAO,IAAI;IACb;IACA,IAAI,CAACmB,MAAM,GAAG,IAAI,CAAClB,GAAG,CAACiB,IAAI,CAAC;IAC5B,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACH,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAG,SAASA,CAACP,IAAI,EAAE;IACd,IAAI,IAAI,CAACM,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACC,SAAS,CAACP,IAAI,CAAC;IAC7B;EACF;EAEAQ,UAAUA,CAACH,IAAI,EAAE;IACf,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACE,UAAU,CAACH,IAAI,CAAC,EAAE;QACjC,QAAQA,IAAI;UACV,KAAK,GAAG;YAAE;cACR,IAAII,EAAE,GAAG,IAAI,CAACtB,KAAK,CAACU,QAAQ;cAC5B,IAAI,CAACY,EAAE,EAAE;gBACPA,EAAE,GAAG,IAAI,CAACtB,KAAK,CAACU,QAAQ,GAAG,EAAE;cAC/B;cACAY,EAAE,CAACC,IAAI,CAAC,IAAI,CAACJ,MAAM,CAACnB,KAAK,CAAC;cAC1B;YACF;UACA,KAAK,GAAG;YACN,IAAI,CAACA,KAAK,GAAG,IAAI,CAACmB,MAAM,CAACnB,KAAK;YAC9B;UACF;YACE;QACJ;QACA,IAAI,CAACmB,MAAM,GAAGL,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,QAAQI,IAAI;MACV,KAAK,IAAI,CAACb,GAAG;QACX,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF;AACF;AAEAmB,MAAM,CAACC,OAAO,GAAG3B,iBAAiB"}