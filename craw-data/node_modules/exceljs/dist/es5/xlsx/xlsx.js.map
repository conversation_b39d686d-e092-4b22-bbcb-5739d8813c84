{"version": 3, "file": "xlsx.js", "names": ["fs", "require", "JSZip", "PassThrough", "ZipStream", "StreamBuf", "utils", "XmlStream", "bufferToString", "StylesXform", "CoreXform", "SharedStringsXform", "RelationshipsXform", "ContentTypesXform", "AppXform", "WorkbookXform", "WorksheetXform", "DrawingXform", "TableXform", "CommentsXform", "VmlNotesXform", "theme1Xml", "fsReadFileAsync", "filename", "options", "Promise", "resolve", "reject", "readFile", "error", "data", "XLSX", "constructor", "workbook", "exists", "Error", "stream", "createReadStream", "read", "close", "parseRels", "xform", "parseStream", "parseWorkbook", "parseSharedStrings", "reconcile", "model", "workbookXform", "worksheetXform", "drawingXform", "tableXform", "drawingOptions", "media", "mediaIndex", "Object", "keys", "drawings", "for<PERSON>ach", "name", "drawing", "drawingRel", "drawingRels", "rels", "reduce", "o", "rel", "Id", "anchors", "anchor", "hyperlinks", "picture", "rId", "hyperlink", "Target", "tableOptions", "styles", "values", "tables", "table", "sheetOptions", "sharedStrings", "date1904", "properties", "comments", "vmlDrawings", "worksheets", "worksheet", "relationships", "worksheetRels", "sheetNo", "worksheetHash", "globalRels", "workbookRels", "sheetDefs", "_processWorksheetEntry", "path", "push", "_processCommentEntry", "_processTableEntry", "_processWorksheetRelsEntry", "_processMediaEntry", "entry", "lastDot", "lastIndexOf", "extension", "substr", "streamBuf", "on", "length", "medium", "type", "buffer", "<PERSON><PERSON><PERSON><PERSON>", "pipe", "_processDrawingEntry", "_processDrawingRelsEntry", "_processVmlDrawingEntry", "vmlDrawing", "_processThemeEntry", "themes", "toString", "createInputStream", "Symbol", "asyncIterator", "chunks", "chunk", "load", "<PERSON><PERSON><PERSON>", "concat", "base64", "from", "zip", "loadAsync", "files", "dir", "entryName", "match", "write", "async", "writableObjectMode", "readableObjectMode", "content", "process", "browser", "chunkSize", "i", "substring", "end", "sheets", "definedNames", "views", "calcProperties", "appXform", "appProperties", "company", "manager", "coreXform", "coreProperties", "assign", "addMedia", "all", "map", "append", "dataimg64", "indexOf", "addDrawings", "relsXform", "prepare", "xml", "toXml", "addTables", "tableXml", "target", "addContentTypes", "addApp", "addCore", "addThemes", "theme1", "addOfficeRels", "Type", "RelType", "OfficeDocument", "CoreProperties", "ExtenderProperties", "addWorkbookRels", "count", "Styles", "Theme", "SharedStrings", "Worksheet", "id", "addSharedStrings", "addStyles", "addWorkbook", "addWorksheets", "relationshipsXform", "commentsXform", "vmlNotesXform", "xmlStream", "render", "_finalize", "finalize", "prepareModel", "creator", "lastModifiedBy", "created", "Date", "modified", "useSharedStrings", "undefined", "useStyles", "<PERSON><PERSON>", "worksheetOptions", "drawingsCount", "commentRefs", "tableCount", "ZipWriter", "writeFile", "createWriteStream", "then", "catch", "err", "writeBuffer", "module", "exports"], "sources": ["../../../lib/xlsx/xlsx.js"], "sourcesContent": ["const fs = require('fs');\nconst JSZip = require('jszip');\nconst {PassThrough} = require('readable-stream');\nconst ZipStream = require('../utils/zip-stream');\nconst StreamBuf = require('../utils/stream-buf');\n\nconst utils = require('../utils/utils');\nconst XmlStream = require('../utils/xml-stream');\nconst {bufferToString} = require('../utils/browser-buffer-decode');\n\nconst StylesXform = require('./xform/style/styles-xform');\n\nconst CoreXform = require('./xform/core/core-xform');\nconst SharedStringsXform = require('./xform/strings/shared-strings-xform');\nconst RelationshipsXform = require('./xform/core/relationships-xform');\nconst ContentTypesXform = require('./xform/core/content-types-xform');\nconst AppXform = require('./xform/core/app-xform');\nconst WorkbookXform = require('./xform/book/workbook-xform');\nconst WorksheetXform = require('./xform/sheet/worksheet-xform');\nconst DrawingXform = require('./xform/drawing/drawing-xform');\nconst TableXform = require('./xform/table/table-xform');\nconst CommentsXform = require('./xform/comment/comments-xform');\nconst VmlNotesXform = require('./xform/comment/vml-notes-xform');\n\nconst theme1Xml = require('./xml/theme1');\n\nfunction fsReadFileAsync(filename, options) {\n  return new Promise((resolve, reject) => {\n    fs.readFile(filename, options, (error, data) => {\n      if (error) {\n        reject(error);\n      } else {\n        resolve(data);\n      }\n    });\n  });\n}\n\nclass XLSX {\n  constructor(workbook) {\n    this.workbook = workbook;\n  }\n\n  // ===============================================================================\n  // Workbook\n  // =========================================================================\n  // Read\n\n  async readFile(filename, options) {\n    if (!(await utils.fs.exists(filename))) {\n      throw new Error(`File not found: ${filename}`);\n    }\n    const stream = fs.createReadStream(filename);\n    try {\n      const workbook = await this.read(stream, options);\n      stream.close();\n      return workbook;\n    } catch (error) {\n      stream.close();\n      throw error;\n    }\n  }\n\n  parseRels(stream) {\n    const xform = new RelationshipsXform();\n    return xform.parseStream(stream);\n  }\n\n  parseWorkbook(stream) {\n    const xform = new WorkbookXform();\n    return xform.parseStream(stream);\n  }\n\n  parseSharedStrings(stream) {\n    const xform = new SharedStringsXform();\n    return xform.parseStream(stream);\n  }\n\n  reconcile(model, options) {\n    const workbookXform = new WorkbookXform();\n    const worksheetXform = new WorksheetXform(options);\n    const drawingXform = new DrawingXform();\n    const tableXform = new TableXform();\n\n    workbookXform.reconcile(model);\n\n    // reconcile drawings with their rels\n    const drawingOptions = {\n      media: model.media,\n      mediaIndex: model.mediaIndex,\n    };\n    Object.keys(model.drawings).forEach(name => {\n      const drawing = model.drawings[name];\n      const drawingRel = model.drawingRels[name];\n      if (drawingRel) {\n        drawingOptions.rels = drawingRel.reduce((o, rel) => {\n          o[rel.Id] = rel;\n          return o;\n        }, {});\n        (drawing.anchors || []).forEach(anchor => {\n          const hyperlinks = anchor.picture && anchor.picture.hyperlinks;\n          if (hyperlinks && drawingOptions.rels[hyperlinks.rId]) {\n            hyperlinks.hyperlink = drawingOptions.rels[hyperlinks.rId].Target;\n            delete hyperlinks.rId;\n          }\n        });\n        drawingXform.reconcile(drawing, drawingOptions);\n      }\n    });\n\n    // reconcile tables with the default styles\n    const tableOptions = {\n      styles: model.styles,\n    };\n    Object.values(model.tables).forEach(table => {\n      tableXform.reconcile(table, tableOptions);\n    });\n\n    const sheetOptions = {\n      styles: model.styles,\n      sharedStrings: model.sharedStrings,\n      media: model.media,\n      mediaIndex: model.mediaIndex,\n      date1904: model.properties && model.properties.date1904,\n      drawings: model.drawings,\n      comments: model.comments,\n      tables: model.tables,\n      vmlDrawings: model.vmlDrawings,\n    };\n    model.worksheets.forEach(worksheet => {\n      worksheet.relationships = model.worksheetRels[worksheet.sheetNo];\n      worksheetXform.reconcile(worksheet, sheetOptions);\n    });\n\n    // delete unnecessary parts\n    delete model.worksheetHash;\n    delete model.worksheetRels;\n    delete model.globalRels;\n    delete model.sharedStrings;\n    delete model.workbookRels;\n    delete model.sheetDefs;\n    delete model.styles;\n    delete model.mediaIndex;\n    delete model.drawings;\n    delete model.drawingRels;\n    delete model.vmlDrawings;\n  }\n\n  async _processWorksheetEntry(stream, model, sheetNo, options, path) {\n    const xform = new WorksheetXform(options);\n    const worksheet = await xform.parseStream(stream);\n    worksheet.sheetNo = sheetNo;\n    model.worksheetHash[path] = worksheet;\n    model.worksheets.push(worksheet);\n  }\n\n  async _processCommentEntry(stream, model, name) {\n    const xform = new CommentsXform();\n    const comments = await xform.parseStream(stream);\n    model.comments[`../${name}.xml`] = comments;\n  }\n\n  async _processTableEntry(stream, model, name) {\n    const xform = new TableXform();\n    const table = await xform.parseStream(stream);\n    model.tables[`../tables/${name}.xml`] = table;\n  }\n\n  async _processWorksheetRelsEntry(stream, model, sheetNo) {\n    const xform = new RelationshipsXform();\n    const relationships = await xform.parseStream(stream);\n    model.worksheetRels[sheetNo] = relationships;\n  }\n\n  async _processMediaEntry(entry, model, filename) {\n    const lastDot = filename.lastIndexOf('.');\n    // if we can't determine extension, ignore it\n    if (lastDot >= 1) {\n      const extension = filename.substr(lastDot + 1);\n      const name = filename.substr(0, lastDot);\n      await new Promise((resolve, reject) => {\n        const streamBuf = new StreamBuf();\n        streamBuf.on('finish', () => {\n          model.mediaIndex[filename] = model.media.length;\n          model.mediaIndex[name] = model.media.length;\n          const medium = {\n            type: 'image',\n            name,\n            extension,\n            buffer: streamBuf.toBuffer(),\n          };\n          model.media.push(medium);\n          resolve();\n        });\n        entry.on('error', error => {\n          reject(error);\n        });\n        entry.pipe(streamBuf);\n      });\n    }\n  }\n\n  async _processDrawingEntry(entry, model, name) {\n    const xform = new DrawingXform();\n    const drawing = await xform.parseStream(entry);\n    model.drawings[name] = drawing;\n  }\n\n  async _processDrawingRelsEntry(entry, model, name) {\n    const xform = new RelationshipsXform();\n    const relationships = await xform.parseStream(entry);\n    model.drawingRels[name] = relationships;\n  }\n\n  async _processVmlDrawingEntry(entry, model, name) {\n    const xform = new VmlNotesXform();\n    const vmlDrawing = await xform.parseStream(entry);\n    model.vmlDrawings[`../drawings/${name}.vml`] = vmlDrawing;\n  }\n\n  async _processThemeEntry(entry, model, name) {\n    await new Promise((resolve, reject) => {\n      // TODO: stream entry into buffer and store the xml in the model.themes[]\n      const stream = new StreamBuf();\n      entry.on('error', reject);\n      stream.on('error', reject);\n      stream.on('finish', () => {\n        model.themes[name] = stream.read().toString();\n        resolve();\n      });\n      entry.pipe(stream);\n    });\n  }\n\n  /**\n   * @deprecated since version 4.0. You should use `#read` instead. Please follow upgrade instruction: https://github.com/exceljs/exceljs/blob/master/UPGRADE-4.0.md\n   */\n  createInputStream() {\n    throw new Error(\n      '`XLSX#createInputStream` is deprecated. You should use `XLSX#read` instead. This method will be removed in version 5.0. Please follow upgrade instruction: https://github.com/exceljs/exceljs/blob/master/UPGRADE-4.0.md'\n    );\n  }\n\n  async read(stream, options) {\n    // TODO: Remove once node v8 is deprecated\n    // Detect and upgrade old streams\n    if (!stream[Symbol.asyncIterator] && stream.pipe) {\n      stream = stream.pipe(new PassThrough());\n    }\n    const chunks = [];\n    for await (const chunk of stream) {\n      chunks.push(chunk);\n    }\n    return this.load(Buffer.concat(chunks), options);\n  }\n\n  async load(data, options) {\n    let buffer;\n    if (options && options.base64) {\n      buffer = Buffer.from(data.toString(), 'base64');\n    } else {\n      buffer = data;\n    }\n\n    const model = {\n      worksheets: [],\n      worksheetHash: {},\n      worksheetRels: [],\n      themes: {},\n      media: [],\n      mediaIndex: {},\n      drawings: {},\n      drawingRels: {},\n      comments: {},\n      tables: {},\n      vmlDrawings: {},\n    };\n\n    const zip = await JSZip.loadAsync(buffer);\n    for (const entry of Object.values(zip.files)) {\n      /* eslint-disable no-await-in-loop */\n      if (!entry.dir) {\n        let entryName = entry.name;\n        if (entryName[0] === '/') {\n          entryName = entryName.substr(1);\n        }\n        let stream;\n        if (\n          entryName.match(/xl\\/media\\//) ||\n          // themes are not parsed as stream\n          entryName.match(/xl\\/theme\\/([a-zA-Z0-9]+)[.]xml/)\n        ) {\n          stream = new PassThrough();\n          stream.write(await entry.async('nodebuffer'));\n        } else {\n          // use object mode to avoid buffer-string convention\n          stream = new PassThrough({\n            writableObjectMode: true,\n            readableObjectMode: true,\n          });\n          let content;\n          // https://www.npmjs.com/package/process\n          if (process.browser) {\n            // running in browser, use TextDecoder if possible\n            content = bufferToString(await entry.async('nodebuffer'));\n          } else {\n            // running in node.js\n            content = await entry.async('string');\n          }\n          const chunkSize = 16 * 1024;\n          for (let i = 0; i < content.length; i += chunkSize) {\n            stream.write(content.substring(i, i + chunkSize));\n          }\n        }\n        stream.end();\n        switch (entryName) {\n          case '_rels/.rels':\n            model.globalRels = await this.parseRels(stream);\n            break;\n\n          case 'xl/workbook.xml': {\n            const workbook = await this.parseWorkbook(stream);\n            model.sheets = workbook.sheets;\n            model.definedNames = workbook.definedNames;\n            model.views = workbook.views;\n            model.properties = workbook.properties;\n            model.calcProperties = workbook.calcProperties;\n            break;\n          }\n\n          case 'xl/_rels/workbook.xml.rels':\n            model.workbookRels = await this.parseRels(stream);\n            break;\n\n          case 'xl/sharedStrings.xml':\n            model.sharedStrings = new SharedStringsXform();\n            await model.sharedStrings.parseStream(stream);\n            break;\n\n          case 'xl/styles.xml':\n            model.styles = new StylesXform();\n            await model.styles.parseStream(stream);\n            break;\n\n          case 'docProps/app.xml': {\n            const appXform = new AppXform();\n            const appProperties = await appXform.parseStream(stream);\n            model.company = appProperties.company;\n            model.manager = appProperties.manager;\n            break;\n          }\n\n          case 'docProps/core.xml': {\n            const coreXform = new CoreXform();\n            const coreProperties = await coreXform.parseStream(stream);\n            Object.assign(model, coreProperties);\n            break;\n          }\n\n          default: {\n            let match = entryName.match(/xl\\/worksheets\\/sheet(\\d+)[.]xml/);\n            if (match) {\n              await this._processWorksheetEntry(stream, model, match[1], options, entryName);\n              break;\n            }\n            match = entryName.match(/xl\\/worksheets\\/_rels\\/sheet(\\d+)[.]xml.rels/);\n            if (match) {\n              await this._processWorksheetRelsEntry(stream, model, match[1]);\n              break;\n            }\n            match = entryName.match(/xl\\/theme\\/([a-zA-Z0-9]+)[.]xml/);\n            if (match) {\n              await this._processThemeEntry(stream, model, match[1]);\n              break;\n            }\n            match = entryName.match(/xl\\/media\\/([a-zA-Z0-9]+[.][a-zA-Z0-9]{3,4})$/);\n            if (match) {\n              await this._processMediaEntry(stream, model, match[1]);\n              break;\n            }\n            match = entryName.match(/xl\\/drawings\\/([a-zA-Z0-9]+)[.]xml/);\n            if (match) {\n              await this._processDrawingEntry(stream, model, match[1]);\n              break;\n            }\n            match = entryName.match(/xl\\/(comments\\d+)[.]xml/);\n            if (match) {\n              await this._processCommentEntry(stream, model, match[1]);\n              break;\n            }\n            match = entryName.match(/xl\\/tables\\/(table\\d+)[.]xml/);\n            if (match) {\n              await this._processTableEntry(stream, model, match[1]);\n              break;\n            }\n            match = entryName.match(/xl\\/drawings\\/_rels\\/([a-zA-Z0-9]+)[.]xml[.]rels/);\n            if (match) {\n              await this._processDrawingRelsEntry(stream, model, match[1]);\n              break;\n            }\n            match = entryName.match(/xl\\/drawings\\/(vmlDrawing\\d+)[.]vml/);\n            if (match) {\n              await this._processVmlDrawingEntry(stream, model, match[1]);\n              break;\n            }\n          }\n        }\n      }\n    }\n\n    this.reconcile(model, options);\n\n    // apply model\n    this.workbook.model = model;\n    return this.workbook;\n  }\n\n  // =========================================================================\n  // Write\n\n  async addMedia(zip, model) {\n    await Promise.all(\n      model.media.map(async medium => {\n        if (medium.type === 'image') {\n          const filename = `xl/media/${medium.name}.${medium.extension}`;\n          if (medium.filename) {\n            const data = await fsReadFileAsync(medium.filename);\n            return zip.append(data, {name: filename});\n          }\n          if (medium.buffer) {\n            return zip.append(medium.buffer, {name: filename});\n          }\n          if (medium.base64) {\n            const dataimg64 = medium.base64;\n            const content = dataimg64.substring(dataimg64.indexOf(',') + 1);\n            return zip.append(content, {name: filename, base64: true});\n          }\n        }\n        throw new Error('Unsupported media');\n      })\n    );\n  }\n\n  addDrawings(zip, model) {\n    const drawingXform = new DrawingXform();\n    const relsXform = new RelationshipsXform();\n\n    model.worksheets.forEach(worksheet => {\n      const {drawing} = worksheet;\n      if (drawing) {\n        drawingXform.prepare(drawing, {});\n        let xml = drawingXform.toXml(drawing);\n        zip.append(xml, {name: `xl/drawings/${drawing.name}.xml`});\n\n        xml = relsXform.toXml(drawing.rels);\n        zip.append(xml, {name: `xl/drawings/_rels/${drawing.name}.xml.rels`});\n      }\n    });\n  }\n\n  addTables(zip, model) {\n    const tableXform = new TableXform();\n\n    model.worksheets.forEach(worksheet => {\n      const {tables} = worksheet;\n      tables.forEach(table => {\n        tableXform.prepare(table, {});\n        const tableXml = tableXform.toXml(table);\n        zip.append(tableXml, {name: `xl/tables/${table.target}`});\n      });\n    });\n  }\n\n  async addContentTypes(zip, model) {\n    const xform = new ContentTypesXform();\n    const xml = xform.toXml(model);\n    zip.append(xml, {name: '[Content_Types].xml'});\n  }\n\n  async addApp(zip, model) {\n    const xform = new AppXform();\n    const xml = xform.toXml(model);\n    zip.append(xml, {name: 'docProps/app.xml'});\n  }\n\n  async addCore(zip, model) {\n    const coreXform = new CoreXform();\n    zip.append(coreXform.toXml(model), {name: 'docProps/core.xml'});\n  }\n\n  async addThemes(zip, model) {\n    const themes = model.themes || {theme1: theme1Xml};\n    Object.keys(themes).forEach(name => {\n      const xml = themes[name];\n      const path = `xl/theme/${name}.xml`;\n      zip.append(xml, {name: path});\n    });\n  }\n\n  async addOfficeRels(zip) {\n    const xform = new RelationshipsXform();\n    const xml = xform.toXml([\n      {Id: 'rId1', Type: XLSX.RelType.OfficeDocument, Target: 'xl/workbook.xml'},\n      {Id: 'rId2', Type: XLSX.RelType.CoreProperties, Target: 'docProps/core.xml'},\n      {Id: 'rId3', Type: XLSX.RelType.ExtenderProperties, Target: 'docProps/app.xml'},\n    ]);\n    zip.append(xml, {name: '_rels/.rels'});\n  }\n\n  async addWorkbookRels(zip, model) {\n    let count = 1;\n    const relationships = [\n      {Id: `rId${count++}`, Type: XLSX.RelType.Styles, Target: 'styles.xml'},\n      {Id: `rId${count++}`, Type: XLSX.RelType.Theme, Target: 'theme/theme1.xml'},\n    ];\n    if (model.sharedStrings.count) {\n      relationships.push({\n        Id: `rId${count++}`,\n        Type: XLSX.RelType.SharedStrings,\n        Target: 'sharedStrings.xml',\n      });\n    }\n    model.worksheets.forEach(worksheet => {\n      worksheet.rId = `rId${count++}`;\n      relationships.push({\n        Id: worksheet.rId,\n        Type: XLSX.RelType.Worksheet,\n        Target: `worksheets/sheet${worksheet.id}.xml`,\n      });\n    });\n    const xform = new RelationshipsXform();\n    const xml = xform.toXml(relationships);\n    zip.append(xml, {name: 'xl/_rels/workbook.xml.rels'});\n  }\n\n  async addSharedStrings(zip, model) {\n    if (model.sharedStrings && model.sharedStrings.count) {\n      zip.append(model.sharedStrings.xml, {name: 'xl/sharedStrings.xml'});\n    }\n  }\n\n  async addStyles(zip, model) {\n    const {xml} = model.styles;\n    if (xml) {\n      zip.append(xml, {name: 'xl/styles.xml'});\n    }\n  }\n\n  async addWorkbook(zip, model) {\n    const xform = new WorkbookXform();\n    zip.append(xform.toXml(model), {name: 'xl/workbook.xml'});\n  }\n\n  async addWorksheets(zip, model) {\n    // preparation phase\n    const worksheetXform = new WorksheetXform();\n    const relationshipsXform = new RelationshipsXform();\n    const commentsXform = new CommentsXform();\n    const vmlNotesXform = new VmlNotesXform();\n\n    // write sheets\n    model.worksheets.forEach(worksheet => {\n      let xmlStream = new XmlStream();\n      worksheetXform.render(xmlStream, worksheet);\n      zip.append(xmlStream.xml, {name: `xl/worksheets/sheet${worksheet.id}.xml`});\n\n      if (worksheet.rels && worksheet.rels.length) {\n        xmlStream = new XmlStream();\n        relationshipsXform.render(xmlStream, worksheet.rels);\n        zip.append(xmlStream.xml, {name: `xl/worksheets/_rels/sheet${worksheet.id}.xml.rels`});\n      }\n\n      if (worksheet.comments.length > 0) {\n        xmlStream = new XmlStream();\n        commentsXform.render(xmlStream, worksheet);\n        zip.append(xmlStream.xml, {name: `xl/comments${worksheet.id}.xml`});\n\n        xmlStream = new XmlStream();\n        vmlNotesXform.render(xmlStream, worksheet);\n        zip.append(xmlStream.xml, {name: `xl/drawings/vmlDrawing${worksheet.id}.vml`});\n      }\n    });\n  }\n\n  _finalize(zip) {\n    return new Promise((resolve, reject) => {\n      zip.on('finish', () => {\n        resolve(this);\n      });\n      zip.on('error', reject);\n      zip.finalize();\n    });\n  }\n\n  prepareModel(model, options) {\n    // ensure following properties have sane values\n    model.creator = model.creator || 'ExcelJS';\n    model.lastModifiedBy = model.lastModifiedBy || 'ExcelJS';\n    model.created = model.created || new Date();\n    model.modified = model.modified || new Date();\n\n    model.useSharedStrings = options.useSharedStrings !== undefined ? options.useSharedStrings : true;\n    model.useStyles = options.useStyles !== undefined ? options.useStyles : true;\n\n    // Manage the shared strings\n    model.sharedStrings = new SharedStringsXform();\n\n    // add a style manager to handle cell formats, fonts, etc.\n    model.styles = model.useStyles ? new StylesXform(true) : new StylesXform.Mock();\n\n    // prepare all of the things before the render\n    const workbookXform = new WorkbookXform();\n    const worksheetXform = new WorksheetXform();\n\n    workbookXform.prepare(model);\n\n    const worksheetOptions = {\n      sharedStrings: model.sharedStrings,\n      styles: model.styles,\n      date1904: model.properties.date1904,\n      drawingsCount: 0,\n      media: model.media,\n    };\n    worksheetOptions.drawings = model.drawings = [];\n    worksheetOptions.commentRefs = model.commentRefs = [];\n    let tableCount = 0;\n    model.tables = [];\n    model.worksheets.forEach(worksheet => {\n      // assign unique filenames to tables\n      worksheet.tables.forEach(table => {\n        tableCount++;\n        table.target = `table${tableCount}.xml`;\n        table.id = tableCount;\n        model.tables.push(table);\n      });\n\n      worksheetXform.prepare(worksheet, worksheetOptions);\n    });\n\n    // TODO: workbook drawing list\n  }\n\n  async write(stream, options) {\n    options = options || {};\n    const {model} = this.workbook;\n    const zip = new ZipStream.ZipWriter(options.zip);\n    zip.pipe(stream);\n\n    this.prepareModel(model, options);\n\n    // render\n    await this.addContentTypes(zip, model);\n    await this.addOfficeRels(zip, model);\n    await this.addWorkbookRels(zip, model);\n    await this.addWorksheets(zip, model);\n    await this.addSharedStrings(zip, model); // always after worksheets\n    await this.addDrawings(zip, model);\n    await this.addTables(zip, model);\n    await Promise.all([this.addThemes(zip, model), this.addStyles(zip, model)]);\n    await this.addMedia(zip, model);\n    await Promise.all([this.addApp(zip, model), this.addCore(zip, model)]);\n    await this.addWorkbook(zip, model);\n    return this._finalize(zip);\n  }\n\n  writeFile(filename, options) {\n    const stream = fs.createWriteStream(filename);\n\n    return new Promise((resolve, reject) => {\n      stream.on('finish', () => {\n        resolve();\n      });\n      stream.on('error', error => {\n        reject(error);\n      });\n\n      this.write(stream, options).then(() => {\n        stream.end();\n      }).catch(err=>{\n        reject(err);\n      });\n    });\n  }\n\n  async writeBuffer(options) {\n    const stream = new StreamBuf();\n    await this.write(stream, options);\n    return stream.read();\n  }\n}\n\nXLSX.RelType = require('./rel-type');\n\nmodule.exports = XLSX;\n"], "mappings": ";;AAAA,MAAMA,EAAE,GAAGC,OAAO,CAAC,IAAI,CAAC;AACxB,MAAMC,KAAK,GAAGD,OAAO,CAAC,OAAO,CAAC;AAC9B,MAAM;EAACE;AAAW,CAAC,GAAGF,OAAO,CAAC,iBAAiB,CAAC;AAChD,MAAMG,SAAS,GAAGH,OAAO,CAAC,qBAAqB,CAAC;AAChD,MAAMI,SAAS,GAAGJ,OAAO,CAAC,qBAAqB,CAAC;AAEhD,MAAMK,KAAK,GAAGL,OAAO,CAAC,gBAAgB,CAAC;AACvC,MAAMM,SAAS,GAAGN,OAAO,CAAC,qBAAqB,CAAC;AAChD,MAAM;EAACO;AAAc,CAAC,GAAGP,OAAO,CAAC,gCAAgC,CAAC;AAElE,MAAMQ,WAAW,GAAGR,OAAO,CAAC,4BAA4B,CAAC;AAEzD,MAAMS,SAAS,GAAGT,OAAO,CAAC,yBAAyB,CAAC;AACpD,MAAMU,kBAAkB,GAAGV,OAAO,CAAC,sCAAsC,CAAC;AAC1E,MAAMW,kBAAkB,GAAGX,OAAO,CAAC,kCAAkC,CAAC;AACtE,MAAMY,iBAAiB,GAAGZ,OAAO,CAAC,kCAAkC,CAAC;AACrE,MAAMa,QAAQ,GAAGb,OAAO,CAAC,wBAAwB,CAAC;AAClD,MAAMc,aAAa,GAAGd,OAAO,CAAC,6BAA6B,CAAC;AAC5D,MAAMe,cAAc,GAAGf,OAAO,CAAC,+BAA+B,CAAC;AAC/D,MAAMgB,YAAY,GAAGhB,OAAO,CAAC,+BAA+B,CAAC;AAC7D,MAAMiB,UAAU,GAAGjB,OAAO,CAAC,2BAA2B,CAAC;AACvD,MAAMkB,aAAa,GAAGlB,OAAO,CAAC,gCAAgC,CAAC;AAC/D,MAAMmB,aAAa,GAAGnB,OAAO,CAAC,iCAAiC,CAAC;AAEhE,MAAMoB,SAAS,GAAGpB,OAAO,CAAC,cAAc,CAAC;AAEzC,SAASqB,eAAeA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EAC1C,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC3B,EAAE,CAAC4B,QAAQ,CAACL,QAAQ,EAAEC,OAAO,EAAE,CAACK,KAAK,EAAEC,IAAI,KAAK;MAC9C,IAAID,KAAK,EAAE;QACTF,MAAM,CAACE,KAAK,CAAC;MACf,CAAC,MAAM;QACLH,OAAO,CAACI,IAAI,CAAC;MACf;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,MAAMC,IAAI,CAAC;EACTC,WAAWA,CAACC,QAAQ,EAAE;IACpB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC1B;;EAEA;EACA;EACA;EACA;;EAEA,MAAML,QAAQA,CAACL,QAAQ,EAAEC,OAAO,EAAE;IAChC,IAAI,EAAE,MAAMlB,KAAK,CAACN,EAAE,CAACkC,MAAM,CAACX,QAAQ,CAAC,CAAC,EAAE;MACtC,MAAM,IAAIY,KAAK,CAAE,mBAAkBZ,QAAS,EAAC,CAAC;IAChD;IACA,MAAMa,MAAM,GAAGpC,EAAE,CAACqC,gBAAgB,CAACd,QAAQ,CAAC;IAC5C,IAAI;MACF,MAAMU,QAAQ,GAAG,MAAM,IAAI,CAACK,IAAI,CAACF,MAAM,EAAEZ,OAAO,CAAC;MACjDY,MAAM,CAACG,KAAK,CAAC,CAAC;MACd,OAAON,QAAQ;IACjB,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdO,MAAM,CAACG,KAAK,CAAC,CAAC;MACd,MAAMV,KAAK;IACb;EACF;EAEAW,SAASA,CAACJ,MAAM,EAAE;IAChB,MAAMK,KAAK,GAAG,IAAI7B,kBAAkB,CAAC,CAAC;IACtC,OAAO6B,KAAK,CAACC,WAAW,CAACN,MAAM,CAAC;EAClC;EAEAO,aAAaA,CAACP,MAAM,EAAE;IACpB,MAAMK,KAAK,GAAG,IAAI1B,aAAa,CAAC,CAAC;IACjC,OAAO0B,KAAK,CAACC,WAAW,CAACN,MAAM,CAAC;EAClC;EAEAQ,kBAAkBA,CAACR,MAAM,EAAE;IACzB,MAAMK,KAAK,GAAG,IAAI9B,kBAAkB,CAAC,CAAC;IACtC,OAAO8B,KAAK,CAACC,WAAW,CAACN,MAAM,CAAC;EAClC;EAEAS,SAASA,CAACC,KAAK,EAAEtB,OAAO,EAAE;IACxB,MAAMuB,aAAa,GAAG,IAAIhC,aAAa,CAAC,CAAC;IACzC,MAAMiC,cAAc,GAAG,IAAIhC,cAAc,CAACQ,OAAO,CAAC;IAClD,MAAMyB,YAAY,GAAG,IAAIhC,YAAY,CAAC,CAAC;IACvC,MAAMiC,UAAU,GAAG,IAAIhC,UAAU,CAAC,CAAC;IAEnC6B,aAAa,CAACF,SAAS,CAACC,KAAK,CAAC;;IAE9B;IACA,MAAMK,cAAc,GAAG;MACrBC,KAAK,EAAEN,KAAK,CAACM,KAAK;MAClBC,UAAU,EAAEP,KAAK,CAACO;IACpB,CAAC;IACDC,MAAM,CAACC,IAAI,CAACT,KAAK,CAACU,QAAQ,CAAC,CAACC,OAAO,CAACC,IAAI,IAAI;MAC1C,MAAMC,OAAO,GAAGb,KAAK,CAACU,QAAQ,CAACE,IAAI,CAAC;MACpC,MAAME,UAAU,GAAGd,KAAK,CAACe,WAAW,CAACH,IAAI,CAAC;MAC1C,IAAIE,UAAU,EAAE;QACdT,cAAc,CAACW,IAAI,GAAGF,UAAU,CAACG,MAAM,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAK;UAClDD,CAAC,CAACC,GAAG,CAACC,EAAE,CAAC,GAAGD,GAAG;UACf,OAAOD,CAAC;QACV,CAAC,EAAE,CAAC,CAAC,CAAC;QACN,CAACL,OAAO,CAACQ,OAAO,IAAI,EAAE,EAAEV,OAAO,CAACW,MAAM,IAAI;UACxC,MAAMC,UAAU,GAAGD,MAAM,CAACE,OAAO,IAAIF,MAAM,CAACE,OAAO,CAACD,UAAU;UAC9D,IAAIA,UAAU,IAAIlB,cAAc,CAACW,IAAI,CAACO,UAAU,CAACE,GAAG,CAAC,EAAE;YACrDF,UAAU,CAACG,SAAS,GAAGrB,cAAc,CAACW,IAAI,CAACO,UAAU,CAACE,GAAG,CAAC,CAACE,MAAM;YACjE,OAAOJ,UAAU,CAACE,GAAG;UACvB;QACF,CAAC,CAAC;QACFtB,YAAY,CAACJ,SAAS,CAACc,OAAO,EAAER,cAAc,CAAC;MACjD;IACF,CAAC,CAAC;;IAEF;IACA,MAAMuB,YAAY,GAAG;MACnBC,MAAM,EAAE7B,KAAK,CAAC6B;IAChB,CAAC;IACDrB,MAAM,CAACsB,MAAM,CAAC9B,KAAK,CAAC+B,MAAM,CAAC,CAACpB,OAAO,CAACqB,KAAK,IAAI;MAC3C5B,UAAU,CAACL,SAAS,CAACiC,KAAK,EAAEJ,YAAY,CAAC;IAC3C,CAAC,CAAC;IAEF,MAAMK,YAAY,GAAG;MACnBJ,MAAM,EAAE7B,KAAK,CAAC6B,MAAM;MACpBK,aAAa,EAAElC,KAAK,CAACkC,aAAa;MAClC5B,KAAK,EAAEN,KAAK,CAACM,KAAK;MAClBC,UAAU,EAAEP,KAAK,CAACO,UAAU;MAC5B4B,QAAQ,EAAEnC,KAAK,CAACoC,UAAU,IAAIpC,KAAK,CAACoC,UAAU,CAACD,QAAQ;MACvDzB,QAAQ,EAAEV,KAAK,CAACU,QAAQ;MACxB2B,QAAQ,EAAErC,KAAK,CAACqC,QAAQ;MACxBN,MAAM,EAAE/B,KAAK,CAAC+B,MAAM;MACpBO,WAAW,EAAEtC,KAAK,CAACsC;IACrB,CAAC;IACDtC,KAAK,CAACuC,UAAU,CAAC5B,OAAO,CAAC6B,SAAS,IAAI;MACpCA,SAAS,CAACC,aAAa,GAAGzC,KAAK,CAAC0C,aAAa,CAACF,SAAS,CAACG,OAAO,CAAC;MAChEzC,cAAc,CAACH,SAAS,CAACyC,SAAS,EAAEP,YAAY,CAAC;IACnD,CAAC,CAAC;;IAEF;IACA,OAAOjC,KAAK,CAAC4C,aAAa;IAC1B,OAAO5C,KAAK,CAAC0C,aAAa;IAC1B,OAAO1C,KAAK,CAAC6C,UAAU;IACvB,OAAO7C,KAAK,CAACkC,aAAa;IAC1B,OAAOlC,KAAK,CAAC8C,YAAY;IACzB,OAAO9C,KAAK,CAAC+C,SAAS;IACtB,OAAO/C,KAAK,CAAC6B,MAAM;IACnB,OAAO7B,KAAK,CAACO,UAAU;IACvB,OAAOP,KAAK,CAACU,QAAQ;IACrB,OAAOV,KAAK,CAACe,WAAW;IACxB,OAAOf,KAAK,CAACsC,WAAW;EAC1B;EAEA,MAAMU,sBAAsBA,CAAC1D,MAAM,EAAEU,KAAK,EAAE2C,OAAO,EAAEjE,OAAO,EAAEuE,IAAI,EAAE;IAClE,MAAMtD,KAAK,GAAG,IAAIzB,cAAc,CAACQ,OAAO,CAAC;IACzC,MAAM8D,SAAS,GAAG,MAAM7C,KAAK,CAACC,WAAW,CAACN,MAAM,CAAC;IACjDkD,SAAS,CAACG,OAAO,GAAGA,OAAO;IAC3B3C,KAAK,CAAC4C,aAAa,CAACK,IAAI,CAAC,GAAGT,SAAS;IACrCxC,KAAK,CAACuC,UAAU,CAACW,IAAI,CAACV,SAAS,CAAC;EAClC;EAEA,MAAMW,oBAAoBA,CAAC7D,MAAM,EAAEU,KAAK,EAAEY,IAAI,EAAE;IAC9C,MAAMjB,KAAK,GAAG,IAAItB,aAAa,CAAC,CAAC;IACjC,MAAMgE,QAAQ,GAAG,MAAM1C,KAAK,CAACC,WAAW,CAACN,MAAM,CAAC;IAChDU,KAAK,CAACqC,QAAQ,CAAE,MAAKzB,IAAK,MAAK,CAAC,GAAGyB,QAAQ;EAC7C;EAEA,MAAMe,kBAAkBA,CAAC9D,MAAM,EAAEU,KAAK,EAAEY,IAAI,EAAE;IAC5C,MAAMjB,KAAK,GAAG,IAAIvB,UAAU,CAAC,CAAC;IAC9B,MAAM4D,KAAK,GAAG,MAAMrC,KAAK,CAACC,WAAW,CAACN,MAAM,CAAC;IAC7CU,KAAK,CAAC+B,MAAM,CAAE,aAAYnB,IAAK,MAAK,CAAC,GAAGoB,KAAK;EAC/C;EAEA,MAAMqB,0BAA0BA,CAAC/D,MAAM,EAAEU,KAAK,EAAE2C,OAAO,EAAE;IACvD,MAAMhD,KAAK,GAAG,IAAI7B,kBAAkB,CAAC,CAAC;IACtC,MAAM2E,aAAa,GAAG,MAAM9C,KAAK,CAACC,WAAW,CAACN,MAAM,CAAC;IACrDU,KAAK,CAAC0C,aAAa,CAACC,OAAO,CAAC,GAAGF,aAAa;EAC9C;EAEA,MAAMa,kBAAkBA,CAACC,KAAK,EAAEvD,KAAK,EAAEvB,QAAQ,EAAE;IAC/C,MAAM+E,OAAO,GAAG/E,QAAQ,CAACgF,WAAW,CAAC,GAAG,CAAC;IACzC;IACA,IAAID,OAAO,IAAI,CAAC,EAAE;MAChB,MAAME,SAAS,GAAGjF,QAAQ,CAACkF,MAAM,CAACH,OAAO,GAAG,CAAC,CAAC;MAC9C,MAAM5C,IAAI,GAAGnC,QAAQ,CAACkF,MAAM,CAAC,CAAC,EAAEH,OAAO,CAAC;MACxC,MAAM,IAAI7E,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACrC,MAAM+E,SAAS,GAAG,IAAIrG,SAAS,CAAC,CAAC;QACjCqG,SAAS,CAACC,EAAE,CAAC,QAAQ,EAAE,MAAM;UAC3B7D,KAAK,CAACO,UAAU,CAAC9B,QAAQ,CAAC,GAAGuB,KAAK,CAACM,KAAK,CAACwD,MAAM;UAC/C9D,KAAK,CAACO,UAAU,CAACK,IAAI,CAAC,GAAGZ,KAAK,CAACM,KAAK,CAACwD,MAAM;UAC3C,MAAMC,MAAM,GAAG;YACbC,IAAI,EAAE,OAAO;YACbpD,IAAI;YACJ8C,SAAS;YACTO,MAAM,EAAEL,SAAS,CAACM,QAAQ,CAAC;UAC7B,CAAC;UACDlE,KAAK,CAACM,KAAK,CAAC4C,IAAI,CAACa,MAAM,CAAC;UACxBnF,OAAO,CAAC,CAAC;QACX,CAAC,CAAC;QACF2E,KAAK,CAACM,EAAE,CAAC,OAAO,EAAE9E,KAAK,IAAI;UACzBF,MAAM,CAACE,KAAK,CAAC;QACf,CAAC,CAAC;QACFwE,KAAK,CAACY,IAAI,CAACP,SAAS,CAAC;MACvB,CAAC,CAAC;IACJ;EACF;EAEA,MAAMQ,oBAAoBA,CAACb,KAAK,EAAEvD,KAAK,EAAEY,IAAI,EAAE;IAC7C,MAAMjB,KAAK,GAAG,IAAIxB,YAAY,CAAC,CAAC;IAChC,MAAM0C,OAAO,GAAG,MAAMlB,KAAK,CAACC,WAAW,CAAC2D,KAAK,CAAC;IAC9CvD,KAAK,CAACU,QAAQ,CAACE,IAAI,CAAC,GAAGC,OAAO;EAChC;EAEA,MAAMwD,wBAAwBA,CAACd,KAAK,EAAEvD,KAAK,EAAEY,IAAI,EAAE;IACjD,MAAMjB,KAAK,GAAG,IAAI7B,kBAAkB,CAAC,CAAC;IACtC,MAAM2E,aAAa,GAAG,MAAM9C,KAAK,CAACC,WAAW,CAAC2D,KAAK,CAAC;IACpDvD,KAAK,CAACe,WAAW,CAACH,IAAI,CAAC,GAAG6B,aAAa;EACzC;EAEA,MAAM6B,uBAAuBA,CAACf,KAAK,EAAEvD,KAAK,EAAEY,IAAI,EAAE;IAChD,MAAMjB,KAAK,GAAG,IAAIrB,aAAa,CAAC,CAAC;IACjC,MAAMiG,UAAU,GAAG,MAAM5E,KAAK,CAACC,WAAW,CAAC2D,KAAK,CAAC;IACjDvD,KAAK,CAACsC,WAAW,CAAE,eAAc1B,IAAK,MAAK,CAAC,GAAG2D,UAAU;EAC3D;EAEA,MAAMC,kBAAkBA,CAACjB,KAAK,EAAEvD,KAAK,EAAEY,IAAI,EAAE;IAC3C,MAAM,IAAIjC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACrC;MACA,MAAMS,MAAM,GAAG,IAAI/B,SAAS,CAAC,CAAC;MAC9BgG,KAAK,CAACM,EAAE,CAAC,OAAO,EAAEhF,MAAM,CAAC;MACzBS,MAAM,CAACuE,EAAE,CAAC,OAAO,EAAEhF,MAAM,CAAC;MAC1BS,MAAM,CAACuE,EAAE,CAAC,QAAQ,EAAE,MAAM;QACxB7D,KAAK,CAACyE,MAAM,CAAC7D,IAAI,CAAC,GAAGtB,MAAM,CAACE,IAAI,CAAC,CAAC,CAACkF,QAAQ,CAAC,CAAC;QAC7C9F,OAAO,CAAC,CAAC;MACX,CAAC,CAAC;MACF2E,KAAK,CAACY,IAAI,CAAC7E,MAAM,CAAC;IACpB,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACEqF,iBAAiBA,CAAA,EAAG;IAClB,MAAM,IAAItF,KAAK,CACb,0NACF,CAAC;EACH;EAEA,MAAMG,IAAIA,CAACF,MAAM,EAAEZ,OAAO,EAAE;IAC1B;IACA;IACA,IAAI,CAACY,MAAM,CAACsF,MAAM,CAACC,aAAa,CAAC,IAAIvF,MAAM,CAAC6E,IAAI,EAAE;MAChD7E,MAAM,GAAGA,MAAM,CAAC6E,IAAI,CAAC,IAAI9G,WAAW,CAAC,CAAC,CAAC;IACzC;IACA,MAAMyH,MAAM,GAAG,EAAE;IACjB,WAAW,MAAMC,KAAK,IAAIzF,MAAM,EAAE;MAChCwF,MAAM,CAAC5B,IAAI,CAAC6B,KAAK,CAAC;IACpB;IACA,OAAO,IAAI,CAACC,IAAI,CAACC,MAAM,CAACC,MAAM,CAACJ,MAAM,CAAC,EAAEpG,OAAO,CAAC;EAClD;EAEA,MAAMsG,IAAIA,CAAChG,IAAI,EAAEN,OAAO,EAAE;IACxB,IAAIuF,MAAM;IACV,IAAIvF,OAAO,IAAIA,OAAO,CAACyG,MAAM,EAAE;MAC7BlB,MAAM,GAAGgB,MAAM,CAACG,IAAI,CAACpG,IAAI,CAAC0F,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC;IACjD,CAAC,MAAM;MACLT,MAAM,GAAGjF,IAAI;IACf;IAEA,MAAMgB,KAAK,GAAG;MACZuC,UAAU,EAAE,EAAE;MACdK,aAAa,EAAE,CAAC,CAAC;MACjBF,aAAa,EAAE,EAAE;MACjB+B,MAAM,EAAE,CAAC,CAAC;MACVnE,KAAK,EAAE,EAAE;MACTC,UAAU,EAAE,CAAC,CAAC;MACdG,QAAQ,EAAE,CAAC,CAAC;MACZK,WAAW,EAAE,CAAC,CAAC;MACfsB,QAAQ,EAAE,CAAC,CAAC;MACZN,MAAM,EAAE,CAAC,CAAC;MACVO,WAAW,EAAE,CAAC;IAChB,CAAC;IAED,MAAM+C,GAAG,GAAG,MAAMjI,KAAK,CAACkI,SAAS,CAACrB,MAAM,CAAC;IACzC,KAAK,MAAMV,KAAK,IAAI/C,MAAM,CAACsB,MAAM,CAACuD,GAAG,CAACE,KAAK,CAAC,EAAE;MAC5C;MACA,IAAI,CAAChC,KAAK,CAACiC,GAAG,EAAE;QACd,IAAIC,SAAS,GAAGlC,KAAK,CAAC3C,IAAI;QAC1B,IAAI6E,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;UACxBA,SAAS,GAAGA,SAAS,CAAC9B,MAAM,CAAC,CAAC,CAAC;QACjC;QACA,IAAIrE,MAAM;QACV,IACEmG,SAAS,CAACC,KAAK,CAAC,aAAa,CAAC;QAC9B;QACAD,SAAS,CAACC,KAAK,CAAC,iCAAiC,CAAC,EAClD;UACApG,MAAM,GAAG,IAAIjC,WAAW,CAAC,CAAC;UAC1BiC,MAAM,CAACqG,KAAK,CAAC,MAAMpC,KAAK,CAACqC,KAAK,CAAC,YAAY,CAAC,CAAC;QAC/C,CAAC,MAAM;UACL;UACAtG,MAAM,GAAG,IAAIjC,WAAW,CAAC;YACvBwI,kBAAkB,EAAE,IAAI;YACxBC,kBAAkB,EAAE;UACtB,CAAC,CAAC;UACF,IAAIC,OAAO;UACX;UACA,IAAIC,OAAO,CAACC,OAAO,EAAE;YACnB;YACAF,OAAO,GAAGrI,cAAc,CAAC,MAAM6F,KAAK,CAACqC,KAAK,CAAC,YAAY,CAAC,CAAC;UAC3D,CAAC,MAAM;YACL;YACAG,OAAO,GAAG,MAAMxC,KAAK,CAACqC,KAAK,CAAC,QAAQ,CAAC;UACvC;UACA,MAAMM,SAAS,GAAG,EAAE,GAAG,IAAI;UAC3B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,OAAO,CAACjC,MAAM,EAAEqC,CAAC,IAAID,SAAS,EAAE;YAClD5G,MAAM,CAACqG,KAAK,CAACI,OAAO,CAACK,SAAS,CAACD,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAAC,CAAC;UACnD;QACF;QACA5G,MAAM,CAAC+G,GAAG,CAAC,CAAC;QACZ,QAAQZ,SAAS;UACf,KAAK,aAAa;YAChBzF,KAAK,CAAC6C,UAAU,GAAG,MAAM,IAAI,CAACnD,SAAS,CAACJ,MAAM,CAAC;YAC/C;UAEF,KAAK,iBAAiB;YAAE;cACtB,MAAMH,QAAQ,GAAG,MAAM,IAAI,CAACU,aAAa,CAACP,MAAM,CAAC;cACjDU,KAAK,CAACsG,MAAM,GAAGnH,QAAQ,CAACmH,MAAM;cAC9BtG,KAAK,CAACuG,YAAY,GAAGpH,QAAQ,CAACoH,YAAY;cAC1CvG,KAAK,CAACwG,KAAK,GAAGrH,QAAQ,CAACqH,KAAK;cAC5BxG,KAAK,CAACoC,UAAU,GAAGjD,QAAQ,CAACiD,UAAU;cACtCpC,KAAK,CAACyG,cAAc,GAAGtH,QAAQ,CAACsH,cAAc;cAC9C;YACF;UAEA,KAAK,4BAA4B;YAC/BzG,KAAK,CAAC8C,YAAY,GAAG,MAAM,IAAI,CAACpD,SAAS,CAACJ,MAAM,CAAC;YACjD;UAEF,KAAK,sBAAsB;YACzBU,KAAK,CAACkC,aAAa,GAAG,IAAIrE,kBAAkB,CAAC,CAAC;YAC9C,MAAMmC,KAAK,CAACkC,aAAa,CAACtC,WAAW,CAACN,MAAM,CAAC;YAC7C;UAEF,KAAK,eAAe;YAClBU,KAAK,CAAC6B,MAAM,GAAG,IAAIlE,WAAW,CAAC,CAAC;YAChC,MAAMqC,KAAK,CAAC6B,MAAM,CAACjC,WAAW,CAACN,MAAM,CAAC;YACtC;UAEF,KAAK,kBAAkB;YAAE;cACvB,MAAMoH,QAAQ,GAAG,IAAI1I,QAAQ,CAAC,CAAC;cAC/B,MAAM2I,aAAa,GAAG,MAAMD,QAAQ,CAAC9G,WAAW,CAACN,MAAM,CAAC;cACxDU,KAAK,CAAC4G,OAAO,GAAGD,aAAa,CAACC,OAAO;cACrC5G,KAAK,CAAC6G,OAAO,GAAGF,aAAa,CAACE,OAAO;cACrC;YACF;UAEA,KAAK,mBAAmB;YAAE;cACxB,MAAMC,SAAS,GAAG,IAAIlJ,SAAS,CAAC,CAAC;cACjC,MAAMmJ,cAAc,GAAG,MAAMD,SAAS,CAAClH,WAAW,CAACN,MAAM,CAAC;cAC1DkB,MAAM,CAACwG,MAAM,CAAChH,KAAK,EAAE+G,cAAc,CAAC;cACpC;YACF;UAEA;YAAS;cACP,IAAIrB,KAAK,GAAGD,SAAS,CAACC,KAAK,CAAC,kCAAkC,CAAC;cAC/D,IAAIA,KAAK,EAAE;gBACT,MAAM,IAAI,CAAC1C,sBAAsB,CAAC1D,MAAM,EAAEU,KAAK,EAAE0F,KAAK,CAAC,CAAC,CAAC,EAAEhH,OAAO,EAAE+G,SAAS,CAAC;gBAC9E;cACF;cACAC,KAAK,GAAGD,SAAS,CAACC,KAAK,CAAC,8CAA8C,CAAC;cACvE,IAAIA,KAAK,EAAE;gBACT,MAAM,IAAI,CAACrC,0BAA0B,CAAC/D,MAAM,EAAEU,KAAK,EAAE0F,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC9D;cACF;cACAA,KAAK,GAAGD,SAAS,CAACC,KAAK,CAAC,iCAAiC,CAAC;cAC1D,IAAIA,KAAK,EAAE;gBACT,MAAM,IAAI,CAAClB,kBAAkB,CAAClF,MAAM,EAAEU,KAAK,EAAE0F,KAAK,CAAC,CAAC,CAAC,CAAC;gBACtD;cACF;cACAA,KAAK,GAAGD,SAAS,CAACC,KAAK,CAAC,+CAA+C,CAAC;cACxE,IAAIA,KAAK,EAAE;gBACT,MAAM,IAAI,CAACpC,kBAAkB,CAAChE,MAAM,EAAEU,KAAK,EAAE0F,KAAK,CAAC,CAAC,CAAC,CAAC;gBACtD;cACF;cACAA,KAAK,GAAGD,SAAS,CAACC,KAAK,CAAC,oCAAoC,CAAC;cAC7D,IAAIA,KAAK,EAAE;gBACT,MAAM,IAAI,CAACtB,oBAAoB,CAAC9E,MAAM,EAAEU,KAAK,EAAE0F,KAAK,CAAC,CAAC,CAAC,CAAC;gBACxD;cACF;cACAA,KAAK,GAAGD,SAAS,CAACC,KAAK,CAAC,yBAAyB,CAAC;cAClD,IAAIA,KAAK,EAAE;gBACT,MAAM,IAAI,CAACvC,oBAAoB,CAAC7D,MAAM,EAAEU,KAAK,EAAE0F,KAAK,CAAC,CAAC,CAAC,CAAC;gBACxD;cACF;cACAA,KAAK,GAAGD,SAAS,CAACC,KAAK,CAAC,8BAA8B,CAAC;cACvD,IAAIA,KAAK,EAAE;gBACT,MAAM,IAAI,CAACtC,kBAAkB,CAAC9D,MAAM,EAAEU,KAAK,EAAE0F,KAAK,CAAC,CAAC,CAAC,CAAC;gBACtD;cACF;cACAA,KAAK,GAAGD,SAAS,CAACC,KAAK,CAAC,kDAAkD,CAAC;cAC3E,IAAIA,KAAK,EAAE;gBACT,MAAM,IAAI,CAACrB,wBAAwB,CAAC/E,MAAM,EAAEU,KAAK,EAAE0F,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC5D;cACF;cACAA,KAAK,GAAGD,SAAS,CAACC,KAAK,CAAC,qCAAqC,CAAC;cAC9D,IAAIA,KAAK,EAAE;gBACT,MAAM,IAAI,CAACpB,uBAAuB,CAAChF,MAAM,EAAEU,KAAK,EAAE0F,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC3D;cACF;YACF;QACF;MACF;IACF;IAEA,IAAI,CAAC3F,SAAS,CAACC,KAAK,EAAEtB,OAAO,CAAC;;IAE9B;IACA,IAAI,CAACS,QAAQ,CAACa,KAAK,GAAGA,KAAK;IAC3B,OAAO,IAAI,CAACb,QAAQ;EACtB;;EAEA;EACA;;EAEA,MAAM8H,QAAQA,CAAC5B,GAAG,EAAErF,KAAK,EAAE;IACzB,MAAMrB,OAAO,CAACuI,GAAG,CACflH,KAAK,CAACM,KAAK,CAAC6G,GAAG,CAAC,MAAMpD,MAAM,IAAI;MAC9B,IAAIA,MAAM,CAACC,IAAI,KAAK,OAAO,EAAE;QAC3B,MAAMvF,QAAQ,GAAI,YAAWsF,MAAM,CAACnD,IAAK,IAAGmD,MAAM,CAACL,SAAU,EAAC;QAC9D,IAAIK,MAAM,CAACtF,QAAQ,EAAE;UACnB,MAAMO,IAAI,GAAG,MAAMR,eAAe,CAACuF,MAAM,CAACtF,QAAQ,CAAC;UACnD,OAAO4G,GAAG,CAAC+B,MAAM,CAACpI,IAAI,EAAE;YAAC4B,IAAI,EAAEnC;UAAQ,CAAC,CAAC;QAC3C;QACA,IAAIsF,MAAM,CAACE,MAAM,EAAE;UACjB,OAAOoB,GAAG,CAAC+B,MAAM,CAACrD,MAAM,CAACE,MAAM,EAAE;YAACrD,IAAI,EAAEnC;UAAQ,CAAC,CAAC;QACpD;QACA,IAAIsF,MAAM,CAACoB,MAAM,EAAE;UACjB,MAAMkC,SAAS,GAAGtD,MAAM,CAACoB,MAAM;UAC/B,MAAMY,OAAO,GAAGsB,SAAS,CAACjB,SAAS,CAACiB,SAAS,CAACC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;UAC/D,OAAOjC,GAAG,CAAC+B,MAAM,CAACrB,OAAO,EAAE;YAACnF,IAAI,EAAEnC,QAAQ;YAAE0G,MAAM,EAAE;UAAI,CAAC,CAAC;QAC5D;MACF;MACA,MAAM,IAAI9F,KAAK,CAAC,mBAAmB,CAAC;IACtC,CAAC,CACH,CAAC;EACH;EAEAkI,WAAWA,CAAClC,GAAG,EAAErF,KAAK,EAAE;IACtB,MAAMG,YAAY,GAAG,IAAIhC,YAAY,CAAC,CAAC;IACvC,MAAMqJ,SAAS,GAAG,IAAI1J,kBAAkB,CAAC,CAAC;IAE1CkC,KAAK,CAACuC,UAAU,CAAC5B,OAAO,CAAC6B,SAAS,IAAI;MACpC,MAAM;QAAC3B;MAAO,CAAC,GAAG2B,SAAS;MAC3B,IAAI3B,OAAO,EAAE;QACXV,YAAY,CAACsH,OAAO,CAAC5G,OAAO,EAAE,CAAC,CAAC,CAAC;QACjC,IAAI6G,GAAG,GAAGvH,YAAY,CAACwH,KAAK,CAAC9G,OAAO,CAAC;QACrCwE,GAAG,CAAC+B,MAAM,CAACM,GAAG,EAAE;UAAC9G,IAAI,EAAG,eAAcC,OAAO,CAACD,IAAK;QAAK,CAAC,CAAC;QAE1D8G,GAAG,GAAGF,SAAS,CAACG,KAAK,CAAC9G,OAAO,CAACG,IAAI,CAAC;QACnCqE,GAAG,CAAC+B,MAAM,CAACM,GAAG,EAAE;UAAC9G,IAAI,EAAG,qBAAoBC,OAAO,CAACD,IAAK;QAAU,CAAC,CAAC;MACvE;IACF,CAAC,CAAC;EACJ;EAEAgH,SAASA,CAACvC,GAAG,EAAErF,KAAK,EAAE;IACpB,MAAMI,UAAU,GAAG,IAAIhC,UAAU,CAAC,CAAC;IAEnC4B,KAAK,CAACuC,UAAU,CAAC5B,OAAO,CAAC6B,SAAS,IAAI;MACpC,MAAM;QAACT;MAAM,CAAC,GAAGS,SAAS;MAC1BT,MAAM,CAACpB,OAAO,CAACqB,KAAK,IAAI;QACtB5B,UAAU,CAACqH,OAAO,CAACzF,KAAK,EAAE,CAAC,CAAC,CAAC;QAC7B,MAAM6F,QAAQ,GAAGzH,UAAU,CAACuH,KAAK,CAAC3F,KAAK,CAAC;QACxCqD,GAAG,CAAC+B,MAAM,CAACS,QAAQ,EAAE;UAACjH,IAAI,EAAG,aAAYoB,KAAK,CAAC8F,MAAO;QAAC,CAAC,CAAC;MAC3D,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA,MAAMC,eAAeA,CAAC1C,GAAG,EAAErF,KAAK,EAAE;IAChC,MAAML,KAAK,GAAG,IAAI5B,iBAAiB,CAAC,CAAC;IACrC,MAAM2J,GAAG,GAAG/H,KAAK,CAACgI,KAAK,CAAC3H,KAAK,CAAC;IAC9BqF,GAAG,CAAC+B,MAAM,CAACM,GAAG,EAAE;MAAC9G,IAAI,EAAE;IAAqB,CAAC,CAAC;EAChD;EAEA,MAAMoH,MAAMA,CAAC3C,GAAG,EAAErF,KAAK,EAAE;IACvB,MAAML,KAAK,GAAG,IAAI3B,QAAQ,CAAC,CAAC;IAC5B,MAAM0J,GAAG,GAAG/H,KAAK,CAACgI,KAAK,CAAC3H,KAAK,CAAC;IAC9BqF,GAAG,CAAC+B,MAAM,CAACM,GAAG,EAAE;MAAC9G,IAAI,EAAE;IAAkB,CAAC,CAAC;EAC7C;EAEA,MAAMqH,OAAOA,CAAC5C,GAAG,EAAErF,KAAK,EAAE;IACxB,MAAM8G,SAAS,GAAG,IAAIlJ,SAAS,CAAC,CAAC;IACjCyH,GAAG,CAAC+B,MAAM,CAACN,SAAS,CAACa,KAAK,CAAC3H,KAAK,CAAC,EAAE;MAACY,IAAI,EAAE;IAAmB,CAAC,CAAC;EACjE;EAEA,MAAMsH,SAASA,CAAC7C,GAAG,EAAErF,KAAK,EAAE;IAC1B,MAAMyE,MAAM,GAAGzE,KAAK,CAACyE,MAAM,IAAI;MAAC0D,MAAM,EAAE5J;IAAS,CAAC;IAClDiC,MAAM,CAACC,IAAI,CAACgE,MAAM,CAAC,CAAC9D,OAAO,CAACC,IAAI,IAAI;MAClC,MAAM8G,GAAG,GAAGjD,MAAM,CAAC7D,IAAI,CAAC;MACxB,MAAMqC,IAAI,GAAI,YAAWrC,IAAK,MAAK;MACnCyE,GAAG,CAAC+B,MAAM,CAACM,GAAG,EAAE;QAAC9G,IAAI,EAAEqC;MAAI,CAAC,CAAC;IAC/B,CAAC,CAAC;EACJ;EAEA,MAAMmF,aAAaA,CAAC/C,GAAG,EAAE;IACvB,MAAM1F,KAAK,GAAG,IAAI7B,kBAAkB,CAAC,CAAC;IACtC,MAAM4J,GAAG,GAAG/H,KAAK,CAACgI,KAAK,CAAC,CACtB;MAACvG,EAAE,EAAE,MAAM;MAAEiH,IAAI,EAAEpJ,IAAI,CAACqJ,OAAO,CAACC,cAAc;MAAE5G,MAAM,EAAE;IAAiB,CAAC,EAC1E;MAACP,EAAE,EAAE,MAAM;MAAEiH,IAAI,EAAEpJ,IAAI,CAACqJ,OAAO,CAACE,cAAc;MAAE7G,MAAM,EAAE;IAAmB,CAAC,EAC5E;MAACP,EAAE,EAAE,MAAM;MAAEiH,IAAI,EAAEpJ,IAAI,CAACqJ,OAAO,CAACG,kBAAkB;MAAE9G,MAAM,EAAE;IAAkB,CAAC,CAChF,CAAC;IACF0D,GAAG,CAAC+B,MAAM,CAACM,GAAG,EAAE;MAAC9G,IAAI,EAAE;IAAa,CAAC,CAAC;EACxC;EAEA,MAAM8H,eAAeA,CAACrD,GAAG,EAAErF,KAAK,EAAE;IAChC,IAAI2I,KAAK,GAAG,CAAC;IACb,MAAMlG,aAAa,GAAG,CACpB;MAACrB,EAAE,EAAG,MAAKuH,KAAK,EAAG,EAAC;MAAEN,IAAI,EAAEpJ,IAAI,CAACqJ,OAAO,CAACM,MAAM;MAAEjH,MAAM,EAAE;IAAY,CAAC,EACtE;MAACP,EAAE,EAAG,MAAKuH,KAAK,EAAG,EAAC;MAAEN,IAAI,EAAEpJ,IAAI,CAACqJ,OAAO,CAACO,KAAK;MAAElH,MAAM,EAAE;IAAkB,CAAC,CAC5E;IACD,IAAI3B,KAAK,CAACkC,aAAa,CAACyG,KAAK,EAAE;MAC7BlG,aAAa,CAACS,IAAI,CAAC;QACjB9B,EAAE,EAAG,MAAKuH,KAAK,EAAG,EAAC;QACnBN,IAAI,EAAEpJ,IAAI,CAACqJ,OAAO,CAACQ,aAAa;QAChCnH,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;IACA3B,KAAK,CAACuC,UAAU,CAAC5B,OAAO,CAAC6B,SAAS,IAAI;MACpCA,SAAS,CAACf,GAAG,GAAI,MAAKkH,KAAK,EAAG,EAAC;MAC/BlG,aAAa,CAACS,IAAI,CAAC;QACjB9B,EAAE,EAAEoB,SAAS,CAACf,GAAG;QACjB4G,IAAI,EAAEpJ,IAAI,CAACqJ,OAAO,CAACS,SAAS;QAC5BpH,MAAM,EAAG,mBAAkBa,SAAS,CAACwG,EAAG;MAC1C,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,MAAMrJ,KAAK,GAAG,IAAI7B,kBAAkB,CAAC,CAAC;IACtC,MAAM4J,GAAG,GAAG/H,KAAK,CAACgI,KAAK,CAAClF,aAAa,CAAC;IACtC4C,GAAG,CAAC+B,MAAM,CAACM,GAAG,EAAE;MAAC9G,IAAI,EAAE;IAA4B,CAAC,CAAC;EACvD;EAEA,MAAMqI,gBAAgBA,CAAC5D,GAAG,EAAErF,KAAK,EAAE;IACjC,IAAIA,KAAK,CAACkC,aAAa,IAAIlC,KAAK,CAACkC,aAAa,CAACyG,KAAK,EAAE;MACpDtD,GAAG,CAAC+B,MAAM,CAACpH,KAAK,CAACkC,aAAa,CAACwF,GAAG,EAAE;QAAC9G,IAAI,EAAE;MAAsB,CAAC,CAAC;IACrE;EACF;EAEA,MAAMsI,SAASA,CAAC7D,GAAG,EAAErF,KAAK,EAAE;IAC1B,MAAM;MAAC0H;IAAG,CAAC,GAAG1H,KAAK,CAAC6B,MAAM;IAC1B,IAAI6F,GAAG,EAAE;MACPrC,GAAG,CAAC+B,MAAM,CAACM,GAAG,EAAE;QAAC9G,IAAI,EAAE;MAAe,CAAC,CAAC;IAC1C;EACF;EAEA,MAAMuI,WAAWA,CAAC9D,GAAG,EAAErF,KAAK,EAAE;IAC5B,MAAML,KAAK,GAAG,IAAI1B,aAAa,CAAC,CAAC;IACjCoH,GAAG,CAAC+B,MAAM,CAACzH,KAAK,CAACgI,KAAK,CAAC3H,KAAK,CAAC,EAAE;MAACY,IAAI,EAAE;IAAiB,CAAC,CAAC;EAC3D;EAEA,MAAMwI,aAAaA,CAAC/D,GAAG,EAAErF,KAAK,EAAE;IAC9B;IACA,MAAME,cAAc,GAAG,IAAIhC,cAAc,CAAC,CAAC;IAC3C,MAAMmL,kBAAkB,GAAG,IAAIvL,kBAAkB,CAAC,CAAC;IACnD,MAAMwL,aAAa,GAAG,IAAIjL,aAAa,CAAC,CAAC;IACzC,MAAMkL,aAAa,GAAG,IAAIjL,aAAa,CAAC,CAAC;;IAEzC;IACA0B,KAAK,CAACuC,UAAU,CAAC5B,OAAO,CAAC6B,SAAS,IAAI;MACpC,IAAIgH,SAAS,GAAG,IAAI/L,SAAS,CAAC,CAAC;MAC/ByC,cAAc,CAACuJ,MAAM,CAACD,SAAS,EAAEhH,SAAS,CAAC;MAC3C6C,GAAG,CAAC+B,MAAM,CAACoC,SAAS,CAAC9B,GAAG,EAAE;QAAC9G,IAAI,EAAG,sBAAqB4B,SAAS,CAACwG,EAAG;MAAK,CAAC,CAAC;MAE3E,IAAIxG,SAAS,CAACxB,IAAI,IAAIwB,SAAS,CAACxB,IAAI,CAAC8C,MAAM,EAAE;QAC3C0F,SAAS,GAAG,IAAI/L,SAAS,CAAC,CAAC;QAC3B4L,kBAAkB,CAACI,MAAM,CAACD,SAAS,EAAEhH,SAAS,CAACxB,IAAI,CAAC;QACpDqE,GAAG,CAAC+B,MAAM,CAACoC,SAAS,CAAC9B,GAAG,EAAE;UAAC9G,IAAI,EAAG,4BAA2B4B,SAAS,CAACwG,EAAG;QAAU,CAAC,CAAC;MACxF;MAEA,IAAIxG,SAAS,CAACH,QAAQ,CAACyB,MAAM,GAAG,CAAC,EAAE;QACjC0F,SAAS,GAAG,IAAI/L,SAAS,CAAC,CAAC;QAC3B6L,aAAa,CAACG,MAAM,CAACD,SAAS,EAAEhH,SAAS,CAAC;QAC1C6C,GAAG,CAAC+B,MAAM,CAACoC,SAAS,CAAC9B,GAAG,EAAE;UAAC9G,IAAI,EAAG,cAAa4B,SAAS,CAACwG,EAAG;QAAK,CAAC,CAAC;QAEnEQ,SAAS,GAAG,IAAI/L,SAAS,CAAC,CAAC;QAC3B8L,aAAa,CAACE,MAAM,CAACD,SAAS,EAAEhH,SAAS,CAAC;QAC1C6C,GAAG,CAAC+B,MAAM,CAACoC,SAAS,CAAC9B,GAAG,EAAE;UAAC9G,IAAI,EAAG,yBAAwB4B,SAAS,CAACwG,EAAG;QAAK,CAAC,CAAC;MAChF;IACF,CAAC,CAAC;EACJ;EAEAU,SAASA,CAACrE,GAAG,EAAE;IACb,OAAO,IAAI1G,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtCwG,GAAG,CAACxB,EAAE,CAAC,QAAQ,EAAE,MAAM;QACrBjF,OAAO,CAAC,IAAI,CAAC;MACf,CAAC,CAAC;MACFyG,GAAG,CAACxB,EAAE,CAAC,OAAO,EAAEhF,MAAM,CAAC;MACvBwG,GAAG,CAACsE,QAAQ,CAAC,CAAC;IAChB,CAAC,CAAC;EACJ;EAEAC,YAAYA,CAAC5J,KAAK,EAAEtB,OAAO,EAAE;IAC3B;IACAsB,KAAK,CAAC6J,OAAO,GAAG7J,KAAK,CAAC6J,OAAO,IAAI,SAAS;IAC1C7J,KAAK,CAAC8J,cAAc,GAAG9J,KAAK,CAAC8J,cAAc,IAAI,SAAS;IACxD9J,KAAK,CAAC+J,OAAO,GAAG/J,KAAK,CAAC+J,OAAO,IAAI,IAAIC,IAAI,CAAC,CAAC;IAC3ChK,KAAK,CAACiK,QAAQ,GAAGjK,KAAK,CAACiK,QAAQ,IAAI,IAAID,IAAI,CAAC,CAAC;IAE7ChK,KAAK,CAACkK,gBAAgB,GAAGxL,OAAO,CAACwL,gBAAgB,KAAKC,SAAS,GAAGzL,OAAO,CAACwL,gBAAgB,GAAG,IAAI;IACjGlK,KAAK,CAACoK,SAAS,GAAG1L,OAAO,CAAC0L,SAAS,KAAKD,SAAS,GAAGzL,OAAO,CAAC0L,SAAS,GAAG,IAAI;;IAE5E;IACApK,KAAK,CAACkC,aAAa,GAAG,IAAIrE,kBAAkB,CAAC,CAAC;;IAE9C;IACAmC,KAAK,CAAC6B,MAAM,GAAG7B,KAAK,CAACoK,SAAS,GAAG,IAAIzM,WAAW,CAAC,IAAI,CAAC,GAAG,IAAIA,WAAW,CAAC0M,IAAI,CAAC,CAAC;;IAE/E;IACA,MAAMpK,aAAa,GAAG,IAAIhC,aAAa,CAAC,CAAC;IACzC,MAAMiC,cAAc,GAAG,IAAIhC,cAAc,CAAC,CAAC;IAE3C+B,aAAa,CAACwH,OAAO,CAACzH,KAAK,CAAC;IAE5B,MAAMsK,gBAAgB,GAAG;MACvBpI,aAAa,EAAElC,KAAK,CAACkC,aAAa;MAClCL,MAAM,EAAE7B,KAAK,CAAC6B,MAAM;MACpBM,QAAQ,EAAEnC,KAAK,CAACoC,UAAU,CAACD,QAAQ;MACnCoI,aAAa,EAAE,CAAC;MAChBjK,KAAK,EAAEN,KAAK,CAACM;IACf,CAAC;IACDgK,gBAAgB,CAAC5J,QAAQ,GAAGV,KAAK,CAACU,QAAQ,GAAG,EAAE;IAC/C4J,gBAAgB,CAACE,WAAW,GAAGxK,KAAK,CAACwK,WAAW,GAAG,EAAE;IACrD,IAAIC,UAAU,GAAG,CAAC;IAClBzK,KAAK,CAAC+B,MAAM,GAAG,EAAE;IACjB/B,KAAK,CAACuC,UAAU,CAAC5B,OAAO,CAAC6B,SAAS,IAAI;MACpC;MACAA,SAAS,CAACT,MAAM,CAACpB,OAAO,CAACqB,KAAK,IAAI;QAChCyI,UAAU,EAAE;QACZzI,KAAK,CAAC8F,MAAM,GAAI,QAAO2C,UAAW,MAAK;QACvCzI,KAAK,CAACgH,EAAE,GAAGyB,UAAU;QACrBzK,KAAK,CAAC+B,MAAM,CAACmB,IAAI,CAAClB,KAAK,CAAC;MAC1B,CAAC,CAAC;MAEF9B,cAAc,CAACuH,OAAO,CAACjF,SAAS,EAAE8H,gBAAgB,CAAC;IACrD,CAAC,CAAC;;IAEF;EACF;;EAEA,MAAM3E,KAAKA,CAACrG,MAAM,EAAEZ,OAAO,EAAE;IAC3BA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;IACvB,MAAM;MAACsB;IAAK,CAAC,GAAG,IAAI,CAACb,QAAQ;IAC7B,MAAMkG,GAAG,GAAG,IAAI/H,SAAS,CAACoN,SAAS,CAAChM,OAAO,CAAC2G,GAAG,CAAC;IAChDA,GAAG,CAAClB,IAAI,CAAC7E,MAAM,CAAC;IAEhB,IAAI,CAACsK,YAAY,CAAC5J,KAAK,EAAEtB,OAAO,CAAC;;IAEjC;IACA,MAAM,IAAI,CAACqJ,eAAe,CAAC1C,GAAG,EAAErF,KAAK,CAAC;IACtC,MAAM,IAAI,CAACoI,aAAa,CAAC/C,GAAG,EAAErF,KAAK,CAAC;IACpC,MAAM,IAAI,CAAC0I,eAAe,CAACrD,GAAG,EAAErF,KAAK,CAAC;IACtC,MAAM,IAAI,CAACoJ,aAAa,CAAC/D,GAAG,EAAErF,KAAK,CAAC;IACpC,MAAM,IAAI,CAACiJ,gBAAgB,CAAC5D,GAAG,EAAErF,KAAK,CAAC,CAAC,CAAC;IACzC,MAAM,IAAI,CAACuH,WAAW,CAAClC,GAAG,EAAErF,KAAK,CAAC;IAClC,MAAM,IAAI,CAAC4H,SAAS,CAACvC,GAAG,EAAErF,KAAK,CAAC;IAChC,MAAMrB,OAAO,CAACuI,GAAG,CAAC,CAAC,IAAI,CAACgB,SAAS,CAAC7C,GAAG,EAAErF,KAAK,CAAC,EAAE,IAAI,CAACkJ,SAAS,CAAC7D,GAAG,EAAErF,KAAK,CAAC,CAAC,CAAC;IAC3E,MAAM,IAAI,CAACiH,QAAQ,CAAC5B,GAAG,EAAErF,KAAK,CAAC;IAC/B,MAAMrB,OAAO,CAACuI,GAAG,CAAC,CAAC,IAAI,CAACc,MAAM,CAAC3C,GAAG,EAAErF,KAAK,CAAC,EAAE,IAAI,CAACiI,OAAO,CAAC5C,GAAG,EAAErF,KAAK,CAAC,CAAC,CAAC;IACtE,MAAM,IAAI,CAACmJ,WAAW,CAAC9D,GAAG,EAAErF,KAAK,CAAC;IAClC,OAAO,IAAI,CAAC0J,SAAS,CAACrE,GAAG,CAAC;EAC5B;EAEAsF,SAASA,CAAClM,QAAQ,EAAEC,OAAO,EAAE;IAC3B,MAAMY,MAAM,GAAGpC,EAAE,CAAC0N,iBAAiB,CAACnM,QAAQ,CAAC;IAE7C,OAAO,IAAIE,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACtCS,MAAM,CAACuE,EAAE,CAAC,QAAQ,EAAE,MAAM;QACxBjF,OAAO,CAAC,CAAC;MACX,CAAC,CAAC;MACFU,MAAM,CAACuE,EAAE,CAAC,OAAO,EAAE9E,KAAK,IAAI;QAC1BF,MAAM,CAACE,KAAK,CAAC;MACf,CAAC,CAAC;MAEF,IAAI,CAAC4G,KAAK,CAACrG,MAAM,EAAEZ,OAAO,CAAC,CAACmM,IAAI,CAAC,MAAM;QACrCvL,MAAM,CAAC+G,GAAG,CAAC,CAAC;MACd,CAAC,CAAC,CAACyE,KAAK,CAACC,GAAG,IAAE;QACZlM,MAAM,CAACkM,GAAG,CAAC;MACb,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA,MAAMC,WAAWA,CAACtM,OAAO,EAAE;IACzB,MAAMY,MAAM,GAAG,IAAI/B,SAAS,CAAC,CAAC;IAC9B,MAAM,IAAI,CAACoI,KAAK,CAACrG,MAAM,EAAEZ,OAAO,CAAC;IACjC,OAAOY,MAAM,CAACE,IAAI,CAAC,CAAC;EACtB;AACF;AAEAP,IAAI,CAACqJ,OAAO,GAAGnL,OAAO,CAAC,YAAY,CAAC;AAEpC8N,MAAM,CAACC,OAAO,GAAGjM,IAAI"}