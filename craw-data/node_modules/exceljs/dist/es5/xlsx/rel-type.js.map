{"version": 3, "file": "rel-type.js", "names": ["module", "exports", "OfficeDocument", "Worksheet", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SharedStrings", "Styles", "Theme", "Hyperlink", "Image", "CoreProperties", "ExtenderProperties", "Comments", "VmlDrawing", "Table"], "sources": ["../../../lib/xlsx/rel-type.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = {\n  OfficeDocument:\n    'http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument',\n  Worksheet: 'http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet',\n  <PERSON><PERSON><PERSON><PERSON>n: 'http://schemas.openxmlformats.org/officeDocument/2006/relationships/calcChain',\n  SharedStrings:\n    'http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings',\n  Styles: 'http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles',\n  Theme: 'http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme',\n  Hyperlink: 'http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink',\n  Image: 'http://schemas.openxmlformats.org/officeDocument/2006/relationships/image',\n  CoreProperties:\n    'http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties',\n  ExtenderProperties:\n    'http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties',\n  Comments: 'http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments',\n  VmlDrawing: 'http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing',\n  Table: 'http://schemas.openxmlformats.org/officeDocument/2006/relationships/table',\n};\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG;EACfC,cAAc,EACZ,oFAAoF;EACtFC,SAAS,EAAE,+EAA+E;EAC1FC,SAAS,EAAE,+EAA+E;EAC1FC,aAAa,EACX,mFAAmF;EACrFC,MAAM,EAAE,4EAA4E;EACpFC,KAAK,EAAE,2EAA2E;EAClFC,SAAS,EAAE,+EAA+E;EAC1FC,KAAK,EAAE,2EAA2E;EAClFC,cAAc,EACZ,uFAAuF;EACzFC,kBAAkB,EAChB,yFAAyF;EAC3FC,QAAQ,EAAE,8EAA8E;EACxFC,UAAU,EAAE,gFAAgF;EAC5FC,KAAK,EAAE;AACT,CAAC"}