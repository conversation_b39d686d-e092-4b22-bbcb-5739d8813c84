{"version": 3, "file": "range.js", "names": ["co<PERSON><PERSON><PERSON>", "require", "Range", "constructor", "decode", "arguments", "setTLBR", "t", "l", "b", "r", "s", "length", "tl", "decode<PERSON>ddress", "br", "model", "top", "Math", "min", "row", "left", "col", "bottom", "max", "right", "sheetName", "argv", "value", "Array", "tlbr", "decodeEx", "Error", "_serialisedSheetName", "test", "expand", "expandRow", "dimensions", "number", "expandToAddress", "addressStr", "address", "n2l", "$t$l", "$b$r", "range", "$range", "short<PERSON><PERSON><PERSON>", "count", "$shortRange", "toString", "intersects", "other", "contains", "containsEx", "for<PERSON>ach<PERSON><PERSON><PERSON>", "cb", "encodeAddress", "module", "exports"], "sources": ["../../../lib/doc/range.js"], "sourcesContent": ["const colCache = require('../utils/col-cache');\n\n// used by worksheet to calculate sheet dimensions\nclass Range {\n  constructor() {\n    this.decode(arguments);\n  }\n\n  setTLBR(t, l, b, r, s) {\n    if (arguments.length < 4) {\n      // setTLBR(tl, br, s)\n      const tl = colCache.decodeAddress(t);\n      const br = colCache.decodeAddress(l);\n      this.model = {\n        top: Math.min(tl.row, br.row),\n        left: Math.min(tl.col, br.col),\n        bottom: Math.max(tl.row, br.row),\n        right: Math.max(tl.col, br.col),\n        sheetName: b,\n      };\n\n      this.setTLBR(tl.row, tl.col, br.row, br.col, s);\n    } else {\n      // setTLBR(t, l, b, r, s)\n      this.model = {\n        top: Math.min(t, b),\n        left: Math.min(l, r),\n        bottom: Math.max(t, b),\n        right: Math.max(l, r),\n        sheetName: s,\n      };\n    }\n  }\n\n  decode(argv) {\n    switch (argv.length) {\n      case 5: // [t,l,b,r,s]\n        this.setTLBR(argv[0], argv[1], argv[2], argv[3], argv[4]);\n        break;\n      case 4: // [t,l,b,r]\n        this.setTLBR(argv[0], argv[1], argv[2], argv[3]);\n        break;\n\n      case 3: // [tl,br,s]\n        this.setTLBR(argv[0], argv[1], argv[2]);\n        break;\n      case 2: // [tl,br]\n        this.setTLBR(argv[0], argv[1]);\n        break;\n\n      case 1: {\n        const value = argv[0];\n        if (value instanceof Range) {\n          // copy constructor\n          this.model = {\n            top: value.model.top,\n            left: value.model.left,\n            bottom: value.model.bottom,\n            right: value.model.right,\n            sheetName: value.sheetName,\n          };\n        } else if (value instanceof Array) {\n          // an arguments array\n          this.decode(value);\n        } else if (value.top && value.left && value.bottom && value.right) {\n          // a model\n          this.model = {\n            top: value.top,\n            left: value.left,\n            bottom: value.bottom,\n            right: value.right,\n            sheetName: value.sheetName,\n          };\n        } else {\n          // [sheetName!]tl:br\n          const tlbr = colCache.decodeEx(value);\n          if (tlbr.top) {\n            this.model = {\n              top: tlbr.top,\n              left: tlbr.left,\n              bottom: tlbr.bottom,\n              right: tlbr.right,\n              sheetName: tlbr.sheetName,\n            };\n          } else {\n            this.model = {\n              top: tlbr.row,\n              left: tlbr.col,\n              bottom: tlbr.row,\n              right: tlbr.col,\n              sheetName: tlbr.sheetName,\n            };\n          }\n        }\n        break;\n      }\n\n      case 0:\n        this.model = {\n          top: 0,\n          left: 0,\n          bottom: 0,\n          right: 0,\n        };\n        break;\n\n      default:\n        throw new Error(`Invalid number of arguments to _getDimensions() - ${argv.length}`);\n    }\n  }\n\n  get top() {\n    return this.model.top || 1;\n  }\n\n  set top(value) {\n    this.model.top = value;\n  }\n\n  get left() {\n    return this.model.left || 1;\n  }\n\n  set left(value) {\n    this.model.left = value;\n  }\n\n  get bottom() {\n    return this.model.bottom || 1;\n  }\n\n  set bottom(value) {\n    this.model.bottom = value;\n  }\n\n  get right() {\n    return this.model.right || 1;\n  }\n\n  set right(value) {\n    this.model.right = value;\n  }\n\n  get sheetName() {\n    return this.model.sheetName;\n  }\n\n  set sheetName(value) {\n    this.model.sheetName = value;\n  }\n\n  get _serialisedSheetName() {\n    const {sheetName} = this.model;\n    if (sheetName) {\n      if (/^[a-zA-Z0-9]*$/.test(sheetName)) {\n        return `${sheetName}!`;\n      }\n      return `'${sheetName}'!`;\n    }\n    return '';\n  }\n\n  expand(top, left, bottom, right) {\n    if (!this.model.top || top < this.top) this.top = top;\n    if (!this.model.left || left < this.left) this.left = left;\n    if (!this.model.bottom || bottom > this.bottom) this.bottom = bottom;\n    if (!this.model.right || right > this.right) this.right = right;\n  }\n\n  expandRow(row) {\n    if (row) {\n      const {dimensions, number} = row;\n      if (dimensions) {\n        this.expand(number, dimensions.min, number, dimensions.max);\n      }\n    }\n  }\n\n  expandToAddress(addressStr) {\n    const address = colCache.decodeEx(addressStr);\n    this.expand(address.row, address.col, address.row, address.col);\n  }\n\n  get tl() {\n    return colCache.n2l(this.left) + this.top;\n  }\n\n  get $t$l() {\n    return `$${colCache.n2l(this.left)}$${this.top}`;\n  }\n\n  get br() {\n    return colCache.n2l(this.right) + this.bottom;\n  }\n\n  get $b$r() {\n    return `$${colCache.n2l(this.right)}$${this.bottom}`;\n  }\n\n  get range() {\n    return `${this._serialisedSheetName + this.tl}:${this.br}`;\n  }\n\n  get $range() {\n    return `${this._serialisedSheetName + this.$t$l}:${this.$b$r}`;\n  }\n\n  get shortRange() {\n    return this.count > 1 ? this.range : this._serialisedSheetName + this.tl;\n  }\n\n  get $shortRange() {\n    return this.count > 1 ? this.$range : this._serialisedSheetName + this.$t$l;\n  }\n\n  get count() {\n    return (1 + this.bottom - this.top) * (1 + this.right - this.left);\n  }\n\n  toString() {\n    return this.range;\n  }\n\n  intersects(other) {\n    if (other.sheetName && this.sheetName && other.sheetName !== this.sheetName) return false;\n    if (other.bottom < this.top) return false;\n    if (other.top > this.bottom) return false;\n    if (other.right < this.left) return false;\n    if (other.left > this.right) return false;\n    return true;\n  }\n\n  contains(addressStr) {\n    const address = colCache.decodeEx(addressStr);\n    return this.containsEx(address);\n  }\n\n  containsEx(address) {\n    if (address.sheetName && this.sheetName && address.sheetName !== this.sheetName) return false;\n    return (\n      address.row >= this.top &&\n      address.row <= this.bottom &&\n      address.col >= this.left &&\n      address.col <= this.right\n    );\n  }\n\n  forEachAddress(cb) {\n    for (let col = this.left; col <= this.right; col++) {\n      for (let row = this.top; row <= this.bottom; row++) {\n        cb(colCache.encodeAddress(row, col), row, col);\n      }\n    }\n  }\n}\n\nmodule.exports = Range;\n"], "mappings": ";;AAAA,MAAMA,QAAQ,GAAGC,OAAO,CAAC,oBAAoB,CAAC;;AAE9C;AACA,MAAMC,KAAK,CAAC;EACVC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,MAAM,CAACC,SAAS,CAAC;EACxB;EAEAC,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;IACrB,IAAIN,SAAS,CAACO,MAAM,GAAG,CAAC,EAAE;MACxB;MACA,MAAMC,EAAE,GAAGb,QAAQ,CAACc,aAAa,CAACP,CAAC,CAAC;MACpC,MAAMQ,EAAE,GAAGf,QAAQ,CAACc,aAAa,CAACN,CAAC,CAAC;MACpC,IAAI,CAACQ,KAAK,GAAG;QACXC,GAAG,EAAEC,IAAI,CAACC,GAAG,CAACN,EAAE,CAACO,GAAG,EAAEL,EAAE,CAACK,GAAG,CAAC;QAC7BC,IAAI,EAAEH,IAAI,CAACC,GAAG,CAACN,EAAE,CAACS,GAAG,EAAEP,EAAE,CAACO,GAAG,CAAC;QAC9BC,MAAM,EAAEL,IAAI,CAACM,GAAG,CAACX,EAAE,CAACO,GAAG,EAAEL,EAAE,CAACK,GAAG,CAAC;QAChCK,KAAK,EAAEP,IAAI,CAACM,GAAG,CAACX,EAAE,CAACS,GAAG,EAAEP,EAAE,CAACO,GAAG,CAAC;QAC/BI,SAAS,EAAEjB;MACb,CAAC;MAED,IAAI,CAACH,OAAO,CAACO,EAAE,CAACO,GAAG,EAAEP,EAAE,CAACS,GAAG,EAAEP,EAAE,CAACK,GAAG,EAAEL,EAAE,CAACO,GAAG,EAAEX,CAAC,CAAC;IACjD,CAAC,MAAM;MACL;MACA,IAAI,CAACK,KAAK,GAAG;QACXC,GAAG,EAAEC,IAAI,CAACC,GAAG,CAACZ,CAAC,EAAEE,CAAC,CAAC;QACnBY,IAAI,EAAEH,IAAI,CAACC,GAAG,CAACX,CAAC,EAAEE,CAAC,CAAC;QACpBa,MAAM,EAAEL,IAAI,CAACM,GAAG,CAACjB,CAAC,EAAEE,CAAC,CAAC;QACtBgB,KAAK,EAAEP,IAAI,CAACM,GAAG,CAAChB,CAAC,EAAEE,CAAC,CAAC;QACrBgB,SAAS,EAAEf;MACb,CAAC;IACH;EACF;EAEAP,MAAMA,CAACuB,IAAI,EAAE;IACX,QAAQA,IAAI,CAACf,MAAM;MACjB,KAAK,CAAC;QAAE;QACN,IAAI,CAACN,OAAO,CAACqB,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;QACzD;MACF,KAAK,CAAC;QAAE;QACN,IAAI,CAACrB,OAAO,CAACqB,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;QAChD;MAEF,KAAK,CAAC;QAAE;QACN,IAAI,CAACrB,OAAO,CAACqB,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;QACvC;MACF,KAAK,CAAC;QAAE;QACN,IAAI,CAACrB,OAAO,CAACqB,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;QAC9B;MAEF,KAAK,CAAC;QAAE;UACN,MAAMC,KAAK,GAAGD,IAAI,CAAC,CAAC,CAAC;UACrB,IAAIC,KAAK,YAAY1B,KAAK,EAAE;YAC1B;YACA,IAAI,CAACc,KAAK,GAAG;cACXC,GAAG,EAAEW,KAAK,CAACZ,KAAK,CAACC,GAAG;cACpBI,IAAI,EAAEO,KAAK,CAACZ,KAAK,CAACK,IAAI;cACtBE,MAAM,EAAEK,KAAK,CAACZ,KAAK,CAACO,MAAM;cAC1BE,KAAK,EAAEG,KAAK,CAACZ,KAAK,CAACS,KAAK;cACxBC,SAAS,EAAEE,KAAK,CAACF;YACnB,CAAC;UACH,CAAC,MAAM,IAAIE,KAAK,YAAYC,KAAK,EAAE;YACjC;YACA,IAAI,CAACzB,MAAM,CAACwB,KAAK,CAAC;UACpB,CAAC,MAAM,IAAIA,KAAK,CAACX,GAAG,IAAIW,KAAK,CAACP,IAAI,IAAIO,KAAK,CAACL,MAAM,IAAIK,KAAK,CAACH,KAAK,EAAE;YACjE;YACA,IAAI,CAACT,KAAK,GAAG;cACXC,GAAG,EAAEW,KAAK,CAACX,GAAG;cACdI,IAAI,EAAEO,KAAK,CAACP,IAAI;cAChBE,MAAM,EAAEK,KAAK,CAACL,MAAM;cACpBE,KAAK,EAAEG,KAAK,CAACH,KAAK;cAClBC,SAAS,EAAEE,KAAK,CAACF;YACnB,CAAC;UACH,CAAC,MAAM;YACL;YACA,MAAMI,IAAI,GAAG9B,QAAQ,CAAC+B,QAAQ,CAACH,KAAK,CAAC;YACrC,IAAIE,IAAI,CAACb,GAAG,EAAE;cACZ,IAAI,CAACD,KAAK,GAAG;gBACXC,GAAG,EAAEa,IAAI,CAACb,GAAG;gBACbI,IAAI,EAAES,IAAI,CAACT,IAAI;gBACfE,MAAM,EAAEO,IAAI,CAACP,MAAM;gBACnBE,KAAK,EAAEK,IAAI,CAACL,KAAK;gBACjBC,SAAS,EAAEI,IAAI,CAACJ;cAClB,CAAC;YACH,CAAC,MAAM;cACL,IAAI,CAACV,KAAK,GAAG;gBACXC,GAAG,EAAEa,IAAI,CAACV,GAAG;gBACbC,IAAI,EAAES,IAAI,CAACR,GAAG;gBACdC,MAAM,EAAEO,IAAI,CAACV,GAAG;gBAChBK,KAAK,EAAEK,IAAI,CAACR,GAAG;gBACfI,SAAS,EAAEI,IAAI,CAACJ;cAClB,CAAC;YACH;UACF;UACA;QACF;MAEA,KAAK,CAAC;QACJ,IAAI,CAACV,KAAK,GAAG;UACXC,GAAG,EAAE,CAAC;UACNI,IAAI,EAAE,CAAC;UACPE,MAAM,EAAE,CAAC;UACTE,KAAK,EAAE;QACT,CAAC;QACD;MAEF;QACE,MAAM,IAAIO,KAAK,CAAE,qDAAoDL,IAAI,CAACf,MAAO,EAAC,CAAC;IACvF;EACF;EAEA,IAAIK,GAAGA,CAAA,EAAG;IACR,OAAO,IAAI,CAACD,KAAK,CAACC,GAAG,IAAI,CAAC;EAC5B;EAEA,IAAIA,GAAGA,CAACW,KAAK,EAAE;IACb,IAAI,CAACZ,KAAK,CAACC,GAAG,GAAGW,KAAK;EACxB;EAEA,IAAIP,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAACL,KAAK,CAACK,IAAI,IAAI,CAAC;EAC7B;EAEA,IAAIA,IAAIA,CAACO,KAAK,EAAE;IACd,IAAI,CAACZ,KAAK,CAACK,IAAI,GAAGO,KAAK;EACzB;EAEA,IAAIL,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACP,KAAK,CAACO,MAAM,IAAI,CAAC;EAC/B;EAEA,IAAIA,MAAMA,CAACK,KAAK,EAAE;IAChB,IAAI,CAACZ,KAAK,CAACO,MAAM,GAAGK,KAAK;EAC3B;EAEA,IAAIH,KAAKA,CAAA,EAAG;IACV,OAAO,IAAI,CAACT,KAAK,CAACS,KAAK,IAAI,CAAC;EAC9B;EAEA,IAAIA,KAAKA,CAACG,KAAK,EAAE;IACf,IAAI,CAACZ,KAAK,CAACS,KAAK,GAAGG,KAAK;EAC1B;EAEA,IAAIF,SAASA,CAAA,EAAG;IACd,OAAO,IAAI,CAACV,KAAK,CAACU,SAAS;EAC7B;EAEA,IAAIA,SAASA,CAACE,KAAK,EAAE;IACnB,IAAI,CAACZ,KAAK,CAACU,SAAS,GAAGE,KAAK;EAC9B;EAEA,IAAIK,oBAAoBA,CAAA,EAAG;IACzB,MAAM;MAACP;IAAS,CAAC,GAAG,IAAI,CAACV,KAAK;IAC9B,IAAIU,SAAS,EAAE;MACb,IAAI,gBAAgB,CAACQ,IAAI,CAACR,SAAS,CAAC,EAAE;QACpC,OAAQ,GAAEA,SAAU,GAAE;MACxB;MACA,OAAQ,IAAGA,SAAU,IAAG;IAC1B;IACA,OAAO,EAAE;EACX;EAEAS,MAAMA,CAAClB,GAAG,EAAEI,IAAI,EAAEE,MAAM,EAAEE,KAAK,EAAE;IAC/B,IAAI,CAAC,IAAI,CAACT,KAAK,CAACC,GAAG,IAAIA,GAAG,GAAG,IAAI,CAACA,GAAG,EAAE,IAAI,CAACA,GAAG,GAAGA,GAAG;IACrD,IAAI,CAAC,IAAI,CAACD,KAAK,CAACK,IAAI,IAAIA,IAAI,GAAG,IAAI,CAACA,IAAI,EAAE,IAAI,CAACA,IAAI,GAAGA,IAAI;IAC1D,IAAI,CAAC,IAAI,CAACL,KAAK,CAACO,MAAM,IAAIA,MAAM,GAAG,IAAI,CAACA,MAAM,EAAE,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpE,IAAI,CAAC,IAAI,CAACP,KAAK,CAACS,KAAK,IAAIA,KAAK,GAAG,IAAI,CAACA,KAAK,EAAE,IAAI,CAACA,KAAK,GAAGA,KAAK;EACjE;EAEAW,SAASA,CAAChB,GAAG,EAAE;IACb,IAAIA,GAAG,EAAE;MACP,MAAM;QAACiB,UAAU;QAAEC;MAAM,CAAC,GAAGlB,GAAG;MAChC,IAAIiB,UAAU,EAAE;QACd,IAAI,CAACF,MAAM,CAACG,MAAM,EAAED,UAAU,CAAClB,GAAG,EAAEmB,MAAM,EAAED,UAAU,CAACb,GAAG,CAAC;MAC7D;IACF;EACF;EAEAe,eAAeA,CAACC,UAAU,EAAE;IAC1B,MAAMC,OAAO,GAAGzC,QAAQ,CAAC+B,QAAQ,CAACS,UAAU,CAAC;IAC7C,IAAI,CAACL,MAAM,CAACM,OAAO,CAACrB,GAAG,EAAEqB,OAAO,CAACnB,GAAG,EAAEmB,OAAO,CAACrB,GAAG,EAAEqB,OAAO,CAACnB,GAAG,CAAC;EACjE;EAEA,IAAIT,EAAEA,CAAA,EAAG;IACP,OAAOb,QAAQ,CAAC0C,GAAG,CAAC,IAAI,CAACrB,IAAI,CAAC,GAAG,IAAI,CAACJ,GAAG;EAC3C;EAEA,IAAI0B,IAAIA,CAAA,EAAG;IACT,OAAQ,IAAG3C,QAAQ,CAAC0C,GAAG,CAAC,IAAI,CAACrB,IAAI,CAAE,IAAG,IAAI,CAACJ,GAAI,EAAC;EAClD;EAEA,IAAIF,EAAEA,CAAA,EAAG;IACP,OAAOf,QAAQ,CAAC0C,GAAG,CAAC,IAAI,CAACjB,KAAK,CAAC,GAAG,IAAI,CAACF,MAAM;EAC/C;EAEA,IAAIqB,IAAIA,CAAA,EAAG;IACT,OAAQ,IAAG5C,QAAQ,CAAC0C,GAAG,CAAC,IAAI,CAACjB,KAAK,CAAE,IAAG,IAAI,CAACF,MAAO,EAAC;EACtD;EAEA,IAAIsB,KAAKA,CAAA,EAAG;IACV,OAAQ,GAAE,IAAI,CAACZ,oBAAoB,GAAG,IAAI,CAACpB,EAAG,IAAG,IAAI,CAACE,EAAG,EAAC;EAC5D;EAEA,IAAI+B,MAAMA,CAAA,EAAG;IACX,OAAQ,GAAE,IAAI,CAACb,oBAAoB,GAAG,IAAI,CAACU,IAAK,IAAG,IAAI,CAACC,IAAK,EAAC;EAChE;EAEA,IAAIG,UAAUA,CAAA,EAAG;IACf,OAAO,IAAI,CAACC,KAAK,GAAG,CAAC,GAAG,IAAI,CAACH,KAAK,GAAG,IAAI,CAACZ,oBAAoB,GAAG,IAAI,CAACpB,EAAE;EAC1E;EAEA,IAAIoC,WAAWA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACD,KAAK,GAAG,CAAC,GAAG,IAAI,CAACF,MAAM,GAAG,IAAI,CAACb,oBAAoB,GAAG,IAAI,CAACU,IAAI;EAC7E;EAEA,IAAIK,KAAKA,CAAA,EAAG;IACV,OAAO,CAAC,CAAC,GAAG,IAAI,CAACzB,MAAM,GAAG,IAAI,CAACN,GAAG,KAAK,CAAC,GAAG,IAAI,CAACQ,KAAK,GAAG,IAAI,CAACJ,IAAI,CAAC;EACpE;EAEA6B,QAAQA,CAAA,EAAG;IACT,OAAO,IAAI,CAACL,KAAK;EACnB;EAEAM,UAAUA,CAACC,KAAK,EAAE;IAChB,IAAIA,KAAK,CAAC1B,SAAS,IAAI,IAAI,CAACA,SAAS,IAAI0B,KAAK,CAAC1B,SAAS,KAAK,IAAI,CAACA,SAAS,EAAE,OAAO,KAAK;IACzF,IAAI0B,KAAK,CAAC7B,MAAM,GAAG,IAAI,CAACN,GAAG,EAAE,OAAO,KAAK;IACzC,IAAImC,KAAK,CAACnC,GAAG,GAAG,IAAI,CAACM,MAAM,EAAE,OAAO,KAAK;IACzC,IAAI6B,KAAK,CAAC3B,KAAK,GAAG,IAAI,CAACJ,IAAI,EAAE,OAAO,KAAK;IACzC,IAAI+B,KAAK,CAAC/B,IAAI,GAAG,IAAI,CAACI,KAAK,EAAE,OAAO,KAAK;IACzC,OAAO,IAAI;EACb;EAEA4B,QAAQA,CAACb,UAAU,EAAE;IACnB,MAAMC,OAAO,GAAGzC,QAAQ,CAAC+B,QAAQ,CAACS,UAAU,CAAC;IAC7C,OAAO,IAAI,CAACc,UAAU,CAACb,OAAO,CAAC;EACjC;EAEAa,UAAUA,CAACb,OAAO,EAAE;IAClB,IAAIA,OAAO,CAACf,SAAS,IAAI,IAAI,CAACA,SAAS,IAAIe,OAAO,CAACf,SAAS,KAAK,IAAI,CAACA,SAAS,EAAE,OAAO,KAAK;IAC7F,OACEe,OAAO,CAACrB,GAAG,IAAI,IAAI,CAACH,GAAG,IACvBwB,OAAO,CAACrB,GAAG,IAAI,IAAI,CAACG,MAAM,IAC1BkB,OAAO,CAACnB,GAAG,IAAI,IAAI,CAACD,IAAI,IACxBoB,OAAO,CAACnB,GAAG,IAAI,IAAI,CAACG,KAAK;EAE7B;EAEA8B,cAAcA,CAACC,EAAE,EAAE;IACjB,KAAK,IAAIlC,GAAG,GAAG,IAAI,CAACD,IAAI,EAAEC,GAAG,IAAI,IAAI,CAACG,KAAK,EAAEH,GAAG,EAAE,EAAE;MAClD,KAAK,IAAIF,GAAG,GAAG,IAAI,CAACH,GAAG,EAAEG,GAAG,IAAI,IAAI,CAACG,MAAM,EAAEH,GAAG,EAAE,EAAE;QAClDoC,EAAE,CAACxD,QAAQ,CAACyD,aAAa,CAACrC,GAAG,EAAEE,GAAG,CAAC,EAAEF,GAAG,EAAEE,GAAG,CAAC;MAChD;IACF;EACF;AACF;AAEAoC,MAAM,CAACC,OAAO,GAAGzD,KAAK"}