{"version": 3, "file": "table.js", "names": ["co<PERSON><PERSON><PERSON>", "require", "Column", "constructor", "table", "column", "index", "_set", "name", "value", "cacheState", "filterButton", "style", "totalsRowLabel", "totalsRowFunction", "totalsRowResult", "totalsRowFormula", "Table", "worksheet", "validate", "store", "getFormula", "Error", "width", "columns", "length", "height", "rows", "filterHeight", "headerRow", "tableHeight", "totalsRow", "assign", "o", "dflt", "undefined", "assert", "test", "message", "ref", "tl", "decode<PERSON>ddress", "row", "col", "autoFilterRef", "encode", "tableRef", "for<PERSON>ach", "i", "assignStyle", "cell", "Object", "keys", "key", "count", "r", "getRow", "j", "getCell", "data", "formula", "result", "load", "model", "_cache", "commit", "addRow", "values", "rowNumber", "push", "splice", "removeRows", "rowIndex", "arguments", "getColumn", "colIndex", "addColumn", "removeColumns", "_assign", "target", "prop", "displayName", "disply<PERSON><PERSON>", "displayNamename", "theme", "showFirstColumn", "showLastColumn", "showRowStripes", "showColumnStripes", "module", "exports"], "sources": ["../../../lib/doc/table.js"], "sourcesContent": ["/* eslint-disable max-classes-per-file */\nconst colCache = require('../utils/col-cache');\n\nclass Column {\n  // wrapper around column model, allowing access and manipulation\n  constructor(table, column, index) {\n    this.table = table;\n    this.column = column;\n    this.index = index;\n  }\n\n  _set(name, value) {\n    this.table.cacheState();\n    this.column[name] = value;\n  }\n\n  /* eslint-disable lines-between-class-members */\n  get name() {\n    return this.column.name;\n  }\n  set name(value) {\n    this._set('name', value);\n  }\n\n  get filterButton() {\n    return this.column.filterButton;\n  }\n  set filterButton(value) {\n    this.column.filterButton = value;\n  }\n\n  get style() {\n    return this.column.style;\n  }\n  set style(value) {\n    this.column.style = value;\n  }\n\n  get totalsRowLabel() {\n    return this.column.totalsRowLabel;\n  }\n  set totalsRowLabel(value) {\n    this._set('totalsRowLabel', value);\n  }\n\n  get totalsRowFunction() {\n    return this.column.totalsRowFunction;\n  }\n  set totalsRowFunction(value) {\n    this._set('totalsRowFunction', value);\n  }\n\n  get totalsRowResult() {\n    return this.column.totalsRowResult;\n  }\n  set totalsRowResult(value) {\n    this._set('totalsRowResult', value);\n  }\n\n  get totalsRowFormula() {\n    return this.column.totalsRowFormula;\n  }\n  set totalsRowFormula(value) {\n    this._set('totalsRowFormula', value);\n  }\n  /* eslint-enable lines-between-class-members */\n}\n\nclass Table {\n  constructor(worksheet, table) {\n    this.worksheet = worksheet;\n    if (table) {\n      this.table = table;\n      // check things are ok first\n      this.validate();\n\n      this.store();\n    }\n  }\n\n  getFormula(column) {\n    // get the correct formula to apply to the totals row\n    switch (column.totalsRowFunction) {\n      case 'none':\n        return null;\n      case 'average':\n        return `SUBTOTAL(101,${this.table.name}[${column.name}])`;\n      case 'countNums':\n        return `SUBTOTAL(102,${this.table.name}[${column.name}])`;\n      case 'count':\n        return `SUBTOTAL(103,${this.table.name}[${column.name}])`;\n      case 'max':\n        return `SUBTOTAL(104,${this.table.name}[${column.name}])`;\n      case 'min':\n        return `SUBTOTAL(105,${this.table.name}[${column.name}])`;\n      case 'stdDev':\n        return `SUBTOTAL(106,${this.table.name}[${column.name}])`;\n      case 'var':\n        return `SUBTOTAL(107,${this.table.name}[${column.name}])`;\n      case 'sum':\n        return `SUBTOTAL(109,${this.table.name}[${column.name}])`;\n      case 'custom':\n        return column.totalsRowFormula;\n      default:\n        throw new Error(`Invalid Totals Row Function: ${column.totalsRowFunction}`);\n    }\n  }\n\n  get width() {\n    // width of the table\n    return this.table.columns.length;\n  }\n\n  get height() {\n    // height of the table data\n    return this.table.rows.length;\n  }\n\n  get filterHeight() {\n    // height of the table data plus optional header row\n    return this.height + (this.table.headerRow ? 1 : 0);\n  }\n\n  get tableHeight() {\n    // full height of the table on the sheet\n    return this.filterHeight + (this.table.totalsRow ? 1 : 0);\n  }\n\n  validate() {\n    const {table} = this;\n    // set defaults and check is valid\n    const assign = (o, name, dflt) => {\n      if (o[name] === undefined) {\n        o[name] = dflt;\n      }\n    };\n    assign(table, 'headerRow', true);\n    assign(table, 'totalsRow', false);\n\n    assign(table, 'style', {});\n    assign(table.style, 'theme', 'TableStyleMedium2');\n    assign(table.style, 'showFirstColumn', false);\n    assign(table.style, 'showLastColumn', false);\n    assign(table.style, 'showRowStripes', false);\n    assign(table.style, 'showColumnStripes', false);\n\n    const assert = (test, message) => {\n      if (!test) {\n        throw new Error(message);\n      }\n    };\n    assert(table.ref, 'Table must have ref');\n    assert(table.columns, 'Table must have column definitions');\n    assert(table.rows, 'Table must have row definitions');\n\n    table.tl = colCache.decodeAddress(table.ref);\n    const {row, col} = table.tl;\n    assert(row > 0, 'Table must be on valid row');\n    assert(col > 0, 'Table must be on valid col');\n\n    const {width, filterHeight, tableHeight} = this;\n\n    // autoFilterRef is a range that includes optional headers only\n    table.autoFilterRef = colCache.encode(row, col, row + filterHeight - 1, col + width - 1);\n\n    // tableRef is a range that includes optional headers and totals\n    table.tableRef = colCache.encode(row, col, row + tableHeight - 1, col + width - 1);\n\n    table.columns.forEach((column, i) => {\n      assert(column.name, `Column ${i} must have a name`);\n      if (i === 0) {\n        assign(column, 'totalsRowLabel', 'Total');\n      } else {\n        assign(column, 'totalsRowFunction', 'none');\n        column.totalsRowFormula = this.getFormula(column);\n      }\n    });\n  }\n\n  store() {\n    // where the table needs to store table data, headers, footers in\n    // the sheet...\n    const assignStyle = (cell, style) => {\n      if (style) {\n        Object.keys(style).forEach(key => {\n          cell[key] = style[key];\n        });\n      }\n    };\n\n    const {worksheet, table} = this;\n    const {row, col} = table.tl;\n    let count = 0;\n    if (table.headerRow) {\n      const r = worksheet.getRow(row + count++);\n      table.columns.forEach((column, j) => {\n        const {style, name} = column;\n        const cell = r.getCell(col + j);\n        cell.value = name;\n        assignStyle(cell, style);\n      });\n    }\n    table.rows.forEach(data => {\n      const r = worksheet.getRow(row + count++);\n      data.forEach((value, j) => {\n        const cell = r.getCell(col + j);\n        cell.value = value;\n\n        assignStyle(cell, table.columns[j].style);\n      });\n    });\n\n    if (table.totalsRow) {\n      const r = worksheet.getRow(row + count++);\n      table.columns.forEach((column, j) => {\n        const cell = r.getCell(col + j);\n        if (j === 0) {\n          cell.value = column.totalsRowLabel;\n        } else {\n          const formula = this.getFormula(column);\n          if (formula) {\n            cell.value = {\n              formula: column.totalsRowFormula,\n              result: column.totalsRowResult,\n            };\n          } else {\n            cell.value = null;\n          }\n        }\n\n        assignStyle(cell, column.style);\n      });\n    }\n  }\n\n  load(worksheet) {\n    // where the table will read necessary features from a loaded sheet\n    const {table} = this;\n    const {row, col} = table.tl;\n    let count = 0;\n    if (table.headerRow) {\n      const r = worksheet.getRow(row + count++);\n      table.columns.forEach((column, j) => {\n        const cell = r.getCell(col + j);\n        cell.value = column.name;\n      });\n    }\n    table.rows.forEach(data => {\n      const r = worksheet.getRow(row + count++);\n      data.forEach((value, j) => {\n        const cell = r.getCell(col + j);\n        cell.value = value;\n      });\n    });\n\n    if (table.totalsRow) {\n      const r = worksheet.getRow(row + count++);\n      table.columns.forEach((column, j) => {\n        const cell = r.getCell(col + j);\n        if (j === 0) {\n          cell.value = column.totalsRowLabel;\n        } else {\n          const formula = this.getFormula(column);\n          if (formula) {\n            cell.value = {\n              formula: column.totalsRowFormula,\n              result: column.totalsRowResult,\n            };\n          }\n        }\n      });\n    }\n  }\n\n  get model() {\n    return this.table;\n  }\n\n  set model(value) {\n    this.table = value;\n  }\n\n  // ================================================================\n  // TODO: Mutating methods\n  cacheState() {\n    if (!this._cache) {\n      this._cache = {\n        ref: this.ref,\n        width: this.width,\n        tableHeight: this.tableHeight,\n      };\n    }\n  }\n\n  commit() {\n    // changes may have been made that might have on-sheet effects\n    if (!this._cache) {\n      return;\n    }\n\n    // check things are ok first\n    this.validate();\n\n    const ref = colCache.decodeAddress(this._cache.ref);\n    if (this.ref !== this._cache.ref) {\n      // wipe out whole table footprint at previous location\n      for (let i = 0; i < this._cache.tableHeight; i++) {\n        const row = this.worksheet.getRow(ref.row + i);\n        for (let j = 0; j < this._cache.width; j++) {\n          const cell = row.getCell(ref.col + j);\n          cell.value = null;\n        }\n      }\n    } else {\n      // clear out below table if it has shrunk\n      for (let i = this.tableHeight; i < this._cache.tableHeight; i++) {\n        const row = this.worksheet.getRow(ref.row + i);\n        for (let j = 0; j < this._cache.width; j++) {\n          const cell = row.getCell(ref.col + j);\n          cell.value = null;\n        }\n      }\n\n      // clear out to right of table if it has lost columns\n      for (let i = 0; i < this.tableHeight; i++) {\n        const row = this.worksheet.getRow(ref.row + i);\n        for (let j = this.width; j < this._cache.width; j++) {\n          const cell = row.getCell(ref.col + j);\n          cell.value = null;\n        }\n      }\n    }\n\n    this.store();\n  }\n\n  addRow(values, rowNumber) {\n    // Add a row of data, either insert at rowNumber or append\n    this.cacheState();\n\n    if (rowNumber === undefined) {\n      this.table.rows.push(values);\n    } else {\n      this.table.rows.splice(rowNumber, 0, values);\n    }\n  }\n\n  removeRows(rowIndex, count = 1) {\n    // Remove a rows of data\n    this.cacheState();\n    this.table.rows.splice(rowIndex, count);\n  }\n\n  getColumn(colIndex) {\n    const column = this.table.columns[colIndex];\n    return new Column(this, column, colIndex);\n  }\n\n  addColumn(column, values, colIndex) {\n    // Add a new column, including column defn and values\n    // Inserts at colNumber or adds to the right\n    this.cacheState();\n\n    if (colIndex === undefined) {\n      this.table.columns.push(column);\n      this.table.rows.forEach((row, i) => {\n        row.push(values[i]);\n      });\n    } else {\n      this.table.columns.splice(colIndex, 0, column);\n      this.table.rows.forEach((row, i) => {\n        row.splice(colIndex, 0, values[i]);\n      });\n    }\n  }\n\n  removeColumns(colIndex, count = 1) {\n    // Remove a column with data\n    this.cacheState();\n\n    this.table.columns.splice(colIndex, count);\n    this.table.rows.forEach(row => {\n      row.splice(colIndex, count);\n    });\n  }\n\n  _assign(target, prop, value) {\n    this.cacheState();\n    target[prop] = value;\n  }\n\n  /* eslint-disable lines-between-class-members */\n  get ref() {\n    return this.table.ref;\n  }\n  set ref(value) {\n    this._assign(this.table, 'ref', value);\n  }\n\n  get name() {\n    return this.table.name;\n  }\n  set name(value) {\n    this.table.name = value;\n  }\n\n  get displayName() {\n    return this.table.displyName || this.table.name;\n  }\n  set displayNamename(value) {\n    this.table.displayName = value;\n  }\n\n  get headerRow() {\n    return this.table.headerRow;\n  }\n  set headerRow(value) {\n    this._assign(this.table, 'headerRow', value);\n  }\n\n  get totalsRow() {\n    return this.table.totalsRow;\n  }\n  set totalsRow(value) {\n    this._assign(this.table, 'totalsRow', value);\n  }\n\n  get theme() {\n    return this.table.style.name;\n  }\n  set theme(value) {\n    this.table.style.name = value;\n  }\n\n  get showFirstColumn() {\n    return this.table.style.showFirstColumn;\n  }\n  set showFirstColumn(value) {\n    this.table.style.showFirstColumn = value;\n  }\n\n  get showLastColumn() {\n    return this.table.style.showLastColumn;\n  }\n  set showLastColumn(value) {\n    this.table.style.showLastColumn = value;\n  }\n\n  get showRowStripes() {\n    return this.table.style.showRowStripes;\n  }\n  set showRowStripes(value) {\n    this.table.style.showRowStripes = value;\n  }\n\n  get showColumnStripes() {\n    return this.table.style.showColumnStripes;\n  }\n  set showColumnStripes(value) {\n    this.table.style.showColumnStripes = value;\n  }\n  /* eslint-enable lines-between-class-members */\n}\n\nmodule.exports = Table;\n"], "mappings": ";;AAAA;AACA,MAAMA,QAAQ,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AAE9C,MAAMC,MAAM,CAAC;EACX;EACAC,WAAWA,CAACC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAE;IAChC,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;EACpB;EAEAC,IAAIA,CAACC,IAAI,EAAEC,KAAK,EAAE;IAChB,IAAI,CAACL,KAAK,CAACM,UAAU,CAAC,CAAC;IACvB,IAAI,CAACL,MAAM,CAACG,IAAI,CAAC,GAAGC,KAAK;EAC3B;;EAEA;EACA,IAAID,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAACH,MAAM,CAACG,IAAI;EACzB;EACA,IAAIA,IAAIA,CAACC,KAAK,EAAE;IACd,IAAI,CAACF,IAAI,CAAC,MAAM,EAAEE,KAAK,CAAC;EAC1B;EAEA,IAAIE,YAAYA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACN,MAAM,CAACM,YAAY;EACjC;EACA,IAAIA,YAAYA,CAACF,KAAK,EAAE;IACtB,IAAI,CAACJ,MAAM,CAACM,YAAY,GAAGF,KAAK;EAClC;EAEA,IAAIG,KAAKA,CAAA,EAAG;IACV,OAAO,IAAI,CAACP,MAAM,CAACO,KAAK;EAC1B;EACA,IAAIA,KAAKA,CAACH,KAAK,EAAE;IACf,IAAI,CAACJ,MAAM,CAACO,KAAK,GAAGH,KAAK;EAC3B;EAEA,IAAII,cAAcA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACR,MAAM,CAACQ,cAAc;EACnC;EACA,IAAIA,cAAcA,CAACJ,KAAK,EAAE;IACxB,IAAI,CAACF,IAAI,CAAC,gBAAgB,EAAEE,KAAK,CAAC;EACpC;EAEA,IAAIK,iBAAiBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACT,MAAM,CAACS,iBAAiB;EACtC;EACA,IAAIA,iBAAiBA,CAACL,KAAK,EAAE;IAC3B,IAAI,CAACF,IAAI,CAAC,mBAAmB,EAAEE,KAAK,CAAC;EACvC;EAEA,IAAIM,eAAeA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACV,MAAM,CAACU,eAAe;EACpC;EACA,IAAIA,eAAeA,CAACN,KAAK,EAAE;IACzB,IAAI,CAACF,IAAI,CAAC,iBAAiB,EAAEE,KAAK,CAAC;EACrC;EAEA,IAAIO,gBAAgBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACX,MAAM,CAACW,gBAAgB;EACrC;EACA,IAAIA,gBAAgBA,CAACP,KAAK,EAAE;IAC1B,IAAI,CAACF,IAAI,CAAC,kBAAkB,EAAEE,KAAK,CAAC;EACtC;EACA;AACF;;AAEA,MAAMQ,KAAK,CAAC;EACVd,WAAWA,CAACe,SAAS,EAAEd,KAAK,EAAE;IAC5B,IAAI,CAACc,SAAS,GAAGA,SAAS;IAC1B,IAAId,KAAK,EAAE;MACT,IAAI,CAACA,KAAK,GAAGA,KAAK;MAClB;MACA,IAAI,CAACe,QAAQ,CAAC,CAAC;MAEf,IAAI,CAACC,KAAK,CAAC,CAAC;IACd;EACF;EAEAC,UAAUA,CAAChB,MAAM,EAAE;IACjB;IACA,QAAQA,MAAM,CAACS,iBAAiB;MAC9B,KAAK,MAAM;QACT,OAAO,IAAI;MACb,KAAK,SAAS;QACZ,OAAQ,gBAAe,IAAI,CAACV,KAAK,CAACI,IAAK,IAAGH,MAAM,CAACG,IAAK,IAAG;MAC3D,KAAK,WAAW;QACd,OAAQ,gBAAe,IAAI,CAACJ,KAAK,CAACI,IAAK,IAAGH,MAAM,CAACG,IAAK,IAAG;MAC3D,KAAK,OAAO;QACV,OAAQ,gBAAe,IAAI,CAACJ,KAAK,CAACI,IAAK,IAAGH,MAAM,CAACG,IAAK,IAAG;MAC3D,KAAK,KAAK;QACR,OAAQ,gBAAe,IAAI,CAACJ,KAAK,CAACI,IAAK,IAAGH,MAAM,CAACG,IAAK,IAAG;MAC3D,KAAK,KAAK;QACR,OAAQ,gBAAe,IAAI,CAACJ,KAAK,CAACI,IAAK,IAAGH,MAAM,CAACG,IAAK,IAAG;MAC3D,KAAK,QAAQ;QACX,OAAQ,gBAAe,IAAI,CAACJ,KAAK,CAACI,IAAK,IAAGH,MAAM,CAACG,IAAK,IAAG;MAC3D,KAAK,KAAK;QACR,OAAQ,gBAAe,IAAI,CAACJ,KAAK,CAACI,IAAK,IAAGH,MAAM,CAACG,IAAK,IAAG;MAC3D,KAAK,KAAK;QACR,OAAQ,gBAAe,IAAI,CAACJ,KAAK,CAACI,IAAK,IAAGH,MAAM,CAACG,IAAK,IAAG;MAC3D,KAAK,QAAQ;QACX,OAAOH,MAAM,CAACW,gBAAgB;MAChC;QACE,MAAM,IAAIM,KAAK,CAAE,gCAA+BjB,MAAM,CAACS,iBAAkB,EAAC,CAAC;IAC/E;EACF;EAEA,IAAIS,KAAKA,CAAA,EAAG;IACV;IACA,OAAO,IAAI,CAACnB,KAAK,CAACoB,OAAO,CAACC,MAAM;EAClC;EAEA,IAAIC,MAAMA,CAAA,EAAG;IACX;IACA,OAAO,IAAI,CAACtB,KAAK,CAACuB,IAAI,CAACF,MAAM;EAC/B;EAEA,IAAIG,YAAYA,CAAA,EAAG;IACjB;IACA,OAAO,IAAI,CAACF,MAAM,IAAI,IAAI,CAACtB,KAAK,CAACyB,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;EACrD;EAEA,IAAIC,WAAWA,CAAA,EAAG;IAChB;IACA,OAAO,IAAI,CAACF,YAAY,IAAI,IAAI,CAACxB,KAAK,CAAC2B,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;EAC3D;EAEAZ,QAAQA,CAAA,EAAG;IACT,MAAM;MAACf;IAAK,CAAC,GAAG,IAAI;IACpB;IACA,MAAM4B,MAAM,GAAGA,CAACC,CAAC,EAAEzB,IAAI,EAAE0B,IAAI,KAAK;MAChC,IAAID,CAAC,CAACzB,IAAI,CAAC,KAAK2B,SAAS,EAAE;QACzBF,CAAC,CAACzB,IAAI,CAAC,GAAG0B,IAAI;MAChB;IACF,CAAC;IACDF,MAAM,CAAC5B,KAAK,EAAE,WAAW,EAAE,IAAI,CAAC;IAChC4B,MAAM,CAAC5B,KAAK,EAAE,WAAW,EAAE,KAAK,CAAC;IAEjC4B,MAAM,CAAC5B,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;IAC1B4B,MAAM,CAAC5B,KAAK,CAACQ,KAAK,EAAE,OAAO,EAAE,mBAAmB,CAAC;IACjDoB,MAAM,CAAC5B,KAAK,CAACQ,KAAK,EAAE,iBAAiB,EAAE,KAAK,CAAC;IAC7CoB,MAAM,CAAC5B,KAAK,CAACQ,KAAK,EAAE,gBAAgB,EAAE,KAAK,CAAC;IAC5CoB,MAAM,CAAC5B,KAAK,CAACQ,KAAK,EAAE,gBAAgB,EAAE,KAAK,CAAC;IAC5CoB,MAAM,CAAC5B,KAAK,CAACQ,KAAK,EAAE,mBAAmB,EAAE,KAAK,CAAC;IAE/C,MAAMwB,MAAM,GAAGA,CAACC,IAAI,EAAEC,OAAO,KAAK;MAChC,IAAI,CAACD,IAAI,EAAE;QACT,MAAM,IAAIf,KAAK,CAACgB,OAAO,CAAC;MAC1B;IACF,CAAC;IACDF,MAAM,CAAChC,KAAK,CAACmC,GAAG,EAAE,qBAAqB,CAAC;IACxCH,MAAM,CAAChC,KAAK,CAACoB,OAAO,EAAE,oCAAoC,CAAC;IAC3DY,MAAM,CAAChC,KAAK,CAACuB,IAAI,EAAE,iCAAiC,CAAC;IAErDvB,KAAK,CAACoC,EAAE,GAAGxC,QAAQ,CAACyC,aAAa,CAACrC,KAAK,CAACmC,GAAG,CAAC;IAC5C,MAAM;MAACG,GAAG;MAAEC;IAAG,CAAC,GAAGvC,KAAK,CAACoC,EAAE;IAC3BJ,MAAM,CAACM,GAAG,GAAG,CAAC,EAAE,4BAA4B,CAAC;IAC7CN,MAAM,CAACO,GAAG,GAAG,CAAC,EAAE,4BAA4B,CAAC;IAE7C,MAAM;MAACpB,KAAK;MAAEK,YAAY;MAAEE;IAAW,CAAC,GAAG,IAAI;;IAE/C;IACA1B,KAAK,CAACwC,aAAa,GAAG5C,QAAQ,CAAC6C,MAAM,CAACH,GAAG,EAAEC,GAAG,EAAED,GAAG,GAAGd,YAAY,GAAG,CAAC,EAAEe,GAAG,GAAGpB,KAAK,GAAG,CAAC,CAAC;;IAExF;IACAnB,KAAK,CAAC0C,QAAQ,GAAG9C,QAAQ,CAAC6C,MAAM,CAACH,GAAG,EAAEC,GAAG,EAAED,GAAG,GAAGZ,WAAW,GAAG,CAAC,EAAEa,GAAG,GAAGpB,KAAK,GAAG,CAAC,CAAC;IAElFnB,KAAK,CAACoB,OAAO,CAACuB,OAAO,CAAC,CAAC1C,MAAM,EAAE2C,CAAC,KAAK;MACnCZ,MAAM,CAAC/B,MAAM,CAACG,IAAI,EAAG,UAASwC,CAAE,mBAAkB,CAAC;MACnD,IAAIA,CAAC,KAAK,CAAC,EAAE;QACXhB,MAAM,CAAC3B,MAAM,EAAE,gBAAgB,EAAE,OAAO,CAAC;MAC3C,CAAC,MAAM;QACL2B,MAAM,CAAC3B,MAAM,EAAE,mBAAmB,EAAE,MAAM,CAAC;QAC3CA,MAAM,CAACW,gBAAgB,GAAG,IAAI,CAACK,UAAU,CAAChB,MAAM,CAAC;MACnD;IACF,CAAC,CAAC;EACJ;EAEAe,KAAKA,CAAA,EAAG;IACN;IACA;IACA,MAAM6B,WAAW,GAAGA,CAACC,IAAI,EAAEtC,KAAK,KAAK;MACnC,IAAIA,KAAK,EAAE;QACTuC,MAAM,CAACC,IAAI,CAACxC,KAAK,CAAC,CAACmC,OAAO,CAACM,GAAG,IAAI;UAChCH,IAAI,CAACG,GAAG,CAAC,GAAGzC,KAAK,CAACyC,GAAG,CAAC;QACxB,CAAC,CAAC;MACJ;IACF,CAAC;IAED,MAAM;MAACnC,SAAS;MAAEd;IAAK,CAAC,GAAG,IAAI;IAC/B,MAAM;MAACsC,GAAG;MAAEC;IAAG,CAAC,GAAGvC,KAAK,CAACoC,EAAE;IAC3B,IAAIc,KAAK,GAAG,CAAC;IACb,IAAIlD,KAAK,CAACyB,SAAS,EAAE;MACnB,MAAM0B,CAAC,GAAGrC,SAAS,CAACsC,MAAM,CAACd,GAAG,GAAGY,KAAK,EAAE,CAAC;MACzClD,KAAK,CAACoB,OAAO,CAACuB,OAAO,CAAC,CAAC1C,MAAM,EAAEoD,CAAC,KAAK;QACnC,MAAM;UAAC7C,KAAK;UAAEJ;QAAI,CAAC,GAAGH,MAAM;QAC5B,MAAM6C,IAAI,GAAGK,CAAC,CAACG,OAAO,CAACf,GAAG,GAAGc,CAAC,CAAC;QAC/BP,IAAI,CAACzC,KAAK,GAAGD,IAAI;QACjByC,WAAW,CAACC,IAAI,EAAEtC,KAAK,CAAC;MAC1B,CAAC,CAAC;IACJ;IACAR,KAAK,CAACuB,IAAI,CAACoB,OAAO,CAACY,IAAI,IAAI;MACzB,MAAMJ,CAAC,GAAGrC,SAAS,CAACsC,MAAM,CAACd,GAAG,GAAGY,KAAK,EAAE,CAAC;MACzCK,IAAI,CAACZ,OAAO,CAAC,CAACtC,KAAK,EAAEgD,CAAC,KAAK;QACzB,MAAMP,IAAI,GAAGK,CAAC,CAACG,OAAO,CAACf,GAAG,GAAGc,CAAC,CAAC;QAC/BP,IAAI,CAACzC,KAAK,GAAGA,KAAK;QAElBwC,WAAW,CAACC,IAAI,EAAE9C,KAAK,CAACoB,OAAO,CAACiC,CAAC,CAAC,CAAC7C,KAAK,CAAC;MAC3C,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,IAAIR,KAAK,CAAC2B,SAAS,EAAE;MACnB,MAAMwB,CAAC,GAAGrC,SAAS,CAACsC,MAAM,CAACd,GAAG,GAAGY,KAAK,EAAE,CAAC;MACzClD,KAAK,CAACoB,OAAO,CAACuB,OAAO,CAAC,CAAC1C,MAAM,EAAEoD,CAAC,KAAK;QACnC,MAAMP,IAAI,GAAGK,CAAC,CAACG,OAAO,CAACf,GAAG,GAAGc,CAAC,CAAC;QAC/B,IAAIA,CAAC,KAAK,CAAC,EAAE;UACXP,IAAI,CAACzC,KAAK,GAAGJ,MAAM,CAACQ,cAAc;QACpC,CAAC,MAAM;UACL,MAAM+C,OAAO,GAAG,IAAI,CAACvC,UAAU,CAAChB,MAAM,CAAC;UACvC,IAAIuD,OAAO,EAAE;YACXV,IAAI,CAACzC,KAAK,GAAG;cACXmD,OAAO,EAAEvD,MAAM,CAACW,gBAAgB;cAChC6C,MAAM,EAAExD,MAAM,CAACU;YACjB,CAAC;UACH,CAAC,MAAM;YACLmC,IAAI,CAACzC,KAAK,GAAG,IAAI;UACnB;QACF;QAEAwC,WAAW,CAACC,IAAI,EAAE7C,MAAM,CAACO,KAAK,CAAC;MACjC,CAAC,CAAC;IACJ;EACF;EAEAkD,IAAIA,CAAC5C,SAAS,EAAE;IACd;IACA,MAAM;MAACd;IAAK,CAAC,GAAG,IAAI;IACpB,MAAM;MAACsC,GAAG;MAAEC;IAAG,CAAC,GAAGvC,KAAK,CAACoC,EAAE;IAC3B,IAAIc,KAAK,GAAG,CAAC;IACb,IAAIlD,KAAK,CAACyB,SAAS,EAAE;MACnB,MAAM0B,CAAC,GAAGrC,SAAS,CAACsC,MAAM,CAACd,GAAG,GAAGY,KAAK,EAAE,CAAC;MACzClD,KAAK,CAACoB,OAAO,CAACuB,OAAO,CAAC,CAAC1C,MAAM,EAAEoD,CAAC,KAAK;QACnC,MAAMP,IAAI,GAAGK,CAAC,CAACG,OAAO,CAACf,GAAG,GAAGc,CAAC,CAAC;QAC/BP,IAAI,CAACzC,KAAK,GAAGJ,MAAM,CAACG,IAAI;MAC1B,CAAC,CAAC;IACJ;IACAJ,KAAK,CAACuB,IAAI,CAACoB,OAAO,CAACY,IAAI,IAAI;MACzB,MAAMJ,CAAC,GAAGrC,SAAS,CAACsC,MAAM,CAACd,GAAG,GAAGY,KAAK,EAAE,CAAC;MACzCK,IAAI,CAACZ,OAAO,CAAC,CAACtC,KAAK,EAAEgD,CAAC,KAAK;QACzB,MAAMP,IAAI,GAAGK,CAAC,CAACG,OAAO,CAACf,GAAG,GAAGc,CAAC,CAAC;QAC/BP,IAAI,CAACzC,KAAK,GAAGA,KAAK;MACpB,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,IAAIL,KAAK,CAAC2B,SAAS,EAAE;MACnB,MAAMwB,CAAC,GAAGrC,SAAS,CAACsC,MAAM,CAACd,GAAG,GAAGY,KAAK,EAAE,CAAC;MACzClD,KAAK,CAACoB,OAAO,CAACuB,OAAO,CAAC,CAAC1C,MAAM,EAAEoD,CAAC,KAAK;QACnC,MAAMP,IAAI,GAAGK,CAAC,CAACG,OAAO,CAACf,GAAG,GAAGc,CAAC,CAAC;QAC/B,IAAIA,CAAC,KAAK,CAAC,EAAE;UACXP,IAAI,CAACzC,KAAK,GAAGJ,MAAM,CAACQ,cAAc;QACpC,CAAC,MAAM;UACL,MAAM+C,OAAO,GAAG,IAAI,CAACvC,UAAU,CAAChB,MAAM,CAAC;UACvC,IAAIuD,OAAO,EAAE;YACXV,IAAI,CAACzC,KAAK,GAAG;cACXmD,OAAO,EAAEvD,MAAM,CAACW,gBAAgB;cAChC6C,MAAM,EAAExD,MAAM,CAACU;YACjB,CAAC;UACH;QACF;MACF,CAAC,CAAC;IACJ;EACF;EAEA,IAAIgD,KAAKA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC3D,KAAK;EACnB;EAEA,IAAI2D,KAAKA,CAACtD,KAAK,EAAE;IACf,IAAI,CAACL,KAAK,GAAGK,KAAK;EACpB;;EAEA;EACA;EACAC,UAAUA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACsD,MAAM,EAAE;MAChB,IAAI,CAACA,MAAM,GAAG;QACZzB,GAAG,EAAE,IAAI,CAACA,GAAG;QACbhB,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBO,WAAW,EAAE,IAAI,CAACA;MACpB,CAAC;IACH;EACF;EAEAmC,MAAMA,CAAA,EAAG;IACP;IACA,IAAI,CAAC,IAAI,CAACD,MAAM,EAAE;MAChB;IACF;;IAEA;IACA,IAAI,CAAC7C,QAAQ,CAAC,CAAC;IAEf,MAAMoB,GAAG,GAAGvC,QAAQ,CAACyC,aAAa,CAAC,IAAI,CAACuB,MAAM,CAACzB,GAAG,CAAC;IACnD,IAAI,IAAI,CAACA,GAAG,KAAK,IAAI,CAACyB,MAAM,CAACzB,GAAG,EAAE;MAChC;MACA,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACgB,MAAM,CAAClC,WAAW,EAAEkB,CAAC,EAAE,EAAE;QAChD,MAAMN,GAAG,GAAG,IAAI,CAACxB,SAAS,CAACsC,MAAM,CAACjB,GAAG,CAACG,GAAG,GAAGM,CAAC,CAAC;QAC9C,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACO,MAAM,CAACzC,KAAK,EAAEkC,CAAC,EAAE,EAAE;UAC1C,MAAMP,IAAI,GAAGR,GAAG,CAACgB,OAAO,CAACnB,GAAG,CAACI,GAAG,GAAGc,CAAC,CAAC;UACrCP,IAAI,CAACzC,KAAK,GAAG,IAAI;QACnB;MACF;IACF,CAAC,MAAM;MACL;MACA,KAAK,IAAIuC,CAAC,GAAG,IAAI,CAAClB,WAAW,EAAEkB,CAAC,GAAG,IAAI,CAACgB,MAAM,CAAClC,WAAW,EAAEkB,CAAC,EAAE,EAAE;QAC/D,MAAMN,GAAG,GAAG,IAAI,CAACxB,SAAS,CAACsC,MAAM,CAACjB,GAAG,CAACG,GAAG,GAAGM,CAAC,CAAC;QAC9C,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACO,MAAM,CAACzC,KAAK,EAAEkC,CAAC,EAAE,EAAE;UAC1C,MAAMP,IAAI,GAAGR,GAAG,CAACgB,OAAO,CAACnB,GAAG,CAACI,GAAG,GAAGc,CAAC,CAAC;UACrCP,IAAI,CAACzC,KAAK,GAAG,IAAI;QACnB;MACF;;MAEA;MACA,KAAK,IAAIuC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAClB,WAAW,EAAEkB,CAAC,EAAE,EAAE;QACzC,MAAMN,GAAG,GAAG,IAAI,CAACxB,SAAS,CAACsC,MAAM,CAACjB,GAAG,CAACG,GAAG,GAAGM,CAAC,CAAC;QAC9C,KAAK,IAAIS,CAAC,GAAG,IAAI,CAAClC,KAAK,EAAEkC,CAAC,GAAG,IAAI,CAACO,MAAM,CAACzC,KAAK,EAAEkC,CAAC,EAAE,EAAE;UACnD,MAAMP,IAAI,GAAGR,GAAG,CAACgB,OAAO,CAACnB,GAAG,CAACI,GAAG,GAAGc,CAAC,CAAC;UACrCP,IAAI,CAACzC,KAAK,GAAG,IAAI;QACnB;MACF;IACF;IAEA,IAAI,CAACW,KAAK,CAAC,CAAC;EACd;EAEA8C,MAAMA,CAACC,MAAM,EAAEC,SAAS,EAAE;IACxB;IACA,IAAI,CAAC1D,UAAU,CAAC,CAAC;IAEjB,IAAI0D,SAAS,KAAKjC,SAAS,EAAE;MAC3B,IAAI,CAAC/B,KAAK,CAACuB,IAAI,CAAC0C,IAAI,CAACF,MAAM,CAAC;IAC9B,CAAC,MAAM;MACL,IAAI,CAAC/D,KAAK,CAACuB,IAAI,CAAC2C,MAAM,CAACF,SAAS,EAAE,CAAC,EAAED,MAAM,CAAC;IAC9C;EACF;EAEAI,UAAUA,CAACC,QAAQ,EAAa;IAAA,IAAXlB,KAAK,GAAAmB,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAtC,SAAA,GAAAsC,SAAA,MAAG,CAAC;IAC5B;IACA,IAAI,CAAC/D,UAAU,CAAC,CAAC;IACjB,IAAI,CAACN,KAAK,CAACuB,IAAI,CAAC2C,MAAM,CAACE,QAAQ,EAAElB,KAAK,CAAC;EACzC;EAEAoB,SAASA,CAACC,QAAQ,EAAE;IAClB,MAAMtE,MAAM,GAAG,IAAI,CAACD,KAAK,CAACoB,OAAO,CAACmD,QAAQ,CAAC;IAC3C,OAAO,IAAIzE,MAAM,CAAC,IAAI,EAAEG,MAAM,EAAEsE,QAAQ,CAAC;EAC3C;EAEAC,SAASA,CAACvE,MAAM,EAAE8D,MAAM,EAAEQ,QAAQ,EAAE;IAClC;IACA;IACA,IAAI,CAACjE,UAAU,CAAC,CAAC;IAEjB,IAAIiE,QAAQ,KAAKxC,SAAS,EAAE;MAC1B,IAAI,CAAC/B,KAAK,CAACoB,OAAO,CAAC6C,IAAI,CAAChE,MAAM,CAAC;MAC/B,IAAI,CAACD,KAAK,CAACuB,IAAI,CAACoB,OAAO,CAAC,CAACL,GAAG,EAAEM,CAAC,KAAK;QAClCN,GAAG,CAAC2B,IAAI,CAACF,MAAM,CAACnB,CAAC,CAAC,CAAC;MACrB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAAC5C,KAAK,CAACoB,OAAO,CAAC8C,MAAM,CAACK,QAAQ,EAAE,CAAC,EAAEtE,MAAM,CAAC;MAC9C,IAAI,CAACD,KAAK,CAACuB,IAAI,CAACoB,OAAO,CAAC,CAACL,GAAG,EAAEM,CAAC,KAAK;QAClCN,GAAG,CAAC4B,MAAM,CAACK,QAAQ,EAAE,CAAC,EAAER,MAAM,CAACnB,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC;IACJ;EACF;EAEA6B,aAAaA,CAACF,QAAQ,EAAa;IAAA,IAAXrB,KAAK,GAAAmB,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAtC,SAAA,GAAAsC,SAAA,MAAG,CAAC;IAC/B;IACA,IAAI,CAAC/D,UAAU,CAAC,CAAC;IAEjB,IAAI,CAACN,KAAK,CAACoB,OAAO,CAAC8C,MAAM,CAACK,QAAQ,EAAErB,KAAK,CAAC;IAC1C,IAAI,CAAClD,KAAK,CAACuB,IAAI,CAACoB,OAAO,CAACL,GAAG,IAAI;MAC7BA,GAAG,CAAC4B,MAAM,CAACK,QAAQ,EAAErB,KAAK,CAAC;IAC7B,CAAC,CAAC;EACJ;EAEAwB,OAAOA,CAACC,MAAM,EAAEC,IAAI,EAAEvE,KAAK,EAAE;IAC3B,IAAI,CAACC,UAAU,CAAC,CAAC;IACjBqE,MAAM,CAACC,IAAI,CAAC,GAAGvE,KAAK;EACtB;;EAEA;EACA,IAAI8B,GAAGA,CAAA,EAAG;IACR,OAAO,IAAI,CAACnC,KAAK,CAACmC,GAAG;EACvB;EACA,IAAIA,GAAGA,CAAC9B,KAAK,EAAE;IACb,IAAI,CAACqE,OAAO,CAAC,IAAI,CAAC1E,KAAK,EAAE,KAAK,EAAEK,KAAK,CAAC;EACxC;EAEA,IAAID,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAACJ,KAAK,CAACI,IAAI;EACxB;EACA,IAAIA,IAAIA,CAACC,KAAK,EAAE;IACd,IAAI,CAACL,KAAK,CAACI,IAAI,GAAGC,KAAK;EACzB;EAEA,IAAIwE,WAAWA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC7E,KAAK,CAAC8E,UAAU,IAAI,IAAI,CAAC9E,KAAK,CAACI,IAAI;EACjD;EACA,IAAI2E,eAAeA,CAAC1E,KAAK,EAAE;IACzB,IAAI,CAACL,KAAK,CAAC6E,WAAW,GAAGxE,KAAK;EAChC;EAEA,IAAIoB,SAASA,CAAA,EAAG;IACd,OAAO,IAAI,CAACzB,KAAK,CAACyB,SAAS;EAC7B;EACA,IAAIA,SAASA,CAACpB,KAAK,EAAE;IACnB,IAAI,CAACqE,OAAO,CAAC,IAAI,CAAC1E,KAAK,EAAE,WAAW,EAAEK,KAAK,CAAC;EAC9C;EAEA,IAAIsB,SAASA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC3B,KAAK,CAAC2B,SAAS;EAC7B;EACA,IAAIA,SAASA,CAACtB,KAAK,EAAE;IACnB,IAAI,CAACqE,OAAO,CAAC,IAAI,CAAC1E,KAAK,EAAE,WAAW,EAAEK,KAAK,CAAC;EAC9C;EAEA,IAAI2E,KAAKA,CAAA,EAAG;IACV,OAAO,IAAI,CAAChF,KAAK,CAACQ,KAAK,CAACJ,IAAI;EAC9B;EACA,IAAI4E,KAAKA,CAAC3E,KAAK,EAAE;IACf,IAAI,CAACL,KAAK,CAACQ,KAAK,CAACJ,IAAI,GAAGC,KAAK;EAC/B;EAEA,IAAI4E,eAAeA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACjF,KAAK,CAACQ,KAAK,CAACyE,eAAe;EACzC;EACA,IAAIA,eAAeA,CAAC5E,KAAK,EAAE;IACzB,IAAI,CAACL,KAAK,CAACQ,KAAK,CAACyE,eAAe,GAAG5E,KAAK;EAC1C;EAEA,IAAI6E,cAAcA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAClF,KAAK,CAACQ,KAAK,CAAC0E,cAAc;EACxC;EACA,IAAIA,cAAcA,CAAC7E,KAAK,EAAE;IACxB,IAAI,CAACL,KAAK,CAACQ,KAAK,CAAC0E,cAAc,GAAG7E,KAAK;EACzC;EAEA,IAAI8E,cAAcA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACnF,KAAK,CAACQ,KAAK,CAAC2E,cAAc;EACxC;EACA,IAAIA,cAAcA,CAAC9E,KAAK,EAAE;IACxB,IAAI,CAACL,KAAK,CAACQ,KAAK,CAAC2E,cAAc,GAAG9E,KAAK;EACzC;EAEA,IAAI+E,iBAAiBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACpF,KAAK,CAACQ,KAAK,CAAC4E,iBAAiB;EAC3C;EACA,IAAIA,iBAAiBA,CAAC/E,KAAK,EAAE;IAC3B,IAAI,CAACL,KAAK,CAACQ,KAAK,CAAC4E,iBAAiB,GAAG/E,KAAK;EAC5C;EACA;AACF;;AAEAgF,MAAM,CAACC,OAAO,GAAGzE,KAAK"}