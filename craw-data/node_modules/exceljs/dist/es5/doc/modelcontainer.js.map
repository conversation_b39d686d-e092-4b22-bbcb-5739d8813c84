{"version": 3, "file": "modelcontainer.js", "names": ["XLSX", "require", "ModelContainer", "constructor", "model", "xlsx", "_xlsx", "module", "exports"], "sources": ["../../../lib/doc/modelcontainer.js"], "sourcesContent": ["'use strict';\n\nconst XLSX = require('../xlsx/xlsx');\n\nclass ModelContainer {\n  constructor(model) {\n    this.model = model;\n  }\n\n  get xlsx() {\n    if (!this._xlsx) {\n      this._xlsx = new XLSX(this);\n    }\n    return this._xlsx;\n  }\n}\n\nmodule.exports = ModelContainer;\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,IAAI,GAAGC,OAAO,CAAC,cAAc,CAAC;AAEpC,MAAMC,cAAc,CAAC;EACnBC,WAAWA,CAACC,KAAK,EAAE;IACjB,IAAI,CAACA,KAAK,GAAGA,KAAK;EACpB;EAEA,IAAIC,IAAIA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACC,KAAK,EAAE;MACf,IAAI,CAACA,KAAK,GAAG,IAAIN,IAAI,CAAC,IAAI,CAAC;IAC7B;IACA,OAAO,IAAI,CAACM,KAAK;EACnB;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGN,cAAc"}