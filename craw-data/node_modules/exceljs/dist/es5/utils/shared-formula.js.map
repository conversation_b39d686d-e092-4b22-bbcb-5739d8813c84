{"version": 3, "file": "shared-formula.js", "names": ["co<PERSON><PERSON><PERSON>", "require", "replacementCandidateRx", "CRrx", "slideForm<PERSON>", "formula", "fromCell", "toCell", "offset", "decode", "to", "replace", "refMatch", "sheet", "sheetMaybe", "addrPart", "trailingParen", "match", "exec", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "colStr", "toUpperCase", "<PERSON><PERSON><PERSON><PERSON>", "rowStr", "length", "col", "l2n", "row", "parseInt", "res", "n2l", "module", "exports"], "sources": ["../../../lib/utils/shared-formula.js"], "sourcesContent": ["const colCache = require('./col-cache');\n\n// const cellRefRegex = /(([a-z_\\-0-9]*)!)?[$]?([a-z]+)[$]?([1-9][0-9]*)/i;\nconst replacementCandidateRx = /(([a-z_\\-0-9]*)!)?([a-z0-9_$]{2,})([(])?/gi;\nconst CRrx = /^([$])?([a-z]+)([$])?([1-9][0-9]*)$/i;\n\nfunction slideFormula(formula, fromCell, toCell) {\n  const offset = colCache.decode(fromCell);\n  const to = colCache.decode(toCell);\n  return formula.replace(\n    replacementCandidateRx,\n    (refMatch, sheet, sheetMaybe, addrPart, trailingParen) => {\n      if (trailingParen) {\n        return refMatch;\n      }\n      const match = CRrx.exec(addrPart);\n      if (match) {\n        const colDollar = match[1];\n        const colStr = match[2].toUpperCase();\n        const rowDollar = match[3];\n        const rowStr = match[4];\n        if (colStr.length > 3 || (colStr.length === 3 && colStr > 'XFD')) {\n          // > XFD is the highest col number in excel 2007 and beyond, so this is a named range\n          return refMatch;\n        }\n        let col = colCache.l2n(colStr);\n        let row = parseInt(rowStr, 10);\n        if (!colDollar) {\n          col += to.col - offset.col;\n        }\n        if (!rowDollar) {\n          row += to.row - offset.row;\n        }\n        const res = (sheet || '') + (colDollar || '') + colCache.n2l(col) + (rowDollar || '') + row;\n        return res;\n      }\n      return refMatch;\n    }\n  );\n}\n\nmodule.exports = {\n  slideFormula,\n};\n"], "mappings": ";;AAAA,MAAMA,QAAQ,GAAGC,OAAO,CAAC,aAAa,CAAC;;AAEvC;AACA,MAAMC,sBAAsB,GAAG,4CAA4C;AAC3E,MAAMC,IAAI,GAAG,sCAAsC;AAEnD,SAASC,YAAYA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAE;EAC/C,MAAMC,MAAM,GAAGR,QAAQ,CAACS,MAAM,CAACH,QAAQ,CAAC;EACxC,MAAMI,EAAE,GAAGV,QAAQ,CAACS,MAAM,CAACF,MAAM,CAAC;EAClC,OAAOF,OAAO,CAACM,OAAO,CACpBT,sBAAsB,EACtB,CAACU,QAAQ,EAAEC,KAAK,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,aAAa,KAAK;IACxD,IAAIA,aAAa,EAAE;MACjB,OAAOJ,QAAQ;IACjB;IACA,MAAMK,KAAK,GAAGd,IAAI,CAACe,IAAI,CAACH,QAAQ,CAAC;IACjC,IAAIE,KAAK,EAAE;MACT,MAAME,SAAS,GAAGF,KAAK,CAAC,CAAC,CAAC;MAC1B,MAAMG,MAAM,GAAGH,KAAK,CAAC,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC;MACrC,MAAMC,SAAS,GAAGL,KAAK,CAAC,CAAC,CAAC;MAC1B,MAAMM,MAAM,GAAGN,KAAK,CAAC,CAAC,CAAC;MACvB,IAAIG,MAAM,CAACI,MAAM,GAAG,CAAC,IAAKJ,MAAM,CAACI,MAAM,KAAK,CAAC,IAAIJ,MAAM,GAAG,KAAM,EAAE;QAChE;QACA,OAAOR,QAAQ;MACjB;MACA,IAAIa,GAAG,GAAGzB,QAAQ,CAAC0B,GAAG,CAACN,MAAM,CAAC;MAC9B,IAAIO,GAAG,GAAGC,QAAQ,CAACL,MAAM,EAAE,EAAE,CAAC;MAC9B,IAAI,CAACJ,SAAS,EAAE;QACdM,GAAG,IAAIf,EAAE,CAACe,GAAG,GAAGjB,MAAM,CAACiB,GAAG;MAC5B;MACA,IAAI,CAACH,SAAS,EAAE;QACdK,GAAG,IAAIjB,EAAE,CAACiB,GAAG,GAAGnB,MAAM,CAACmB,GAAG;MAC5B;MACA,MAAME,GAAG,GAAG,CAAChB,KAAK,IAAI,EAAE,KAAKM,SAAS,IAAI,EAAE,CAAC,GAAGnB,QAAQ,CAAC8B,GAAG,CAACL,GAAG,CAAC,IAAIH,SAAS,IAAI,EAAE,CAAC,GAAGK,GAAG;MAC3F,OAAOE,GAAG;IACZ;IACA,OAAOjB,QAAQ;EACjB,CACF,CAAC;AACH;AAEAmB,MAAM,CAACC,OAAO,GAAG;EACf5B;AACF,CAAC"}