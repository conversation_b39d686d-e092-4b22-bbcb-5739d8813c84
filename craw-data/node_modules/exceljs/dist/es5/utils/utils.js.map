{"version": 3, "file": "utils.js", "names": ["fs", "require", "inherits", "cls", "superCtor", "statics", "prototype", "super_", "Object", "keys", "for<PERSON>ach", "i", "defineProperty", "getOwnPropertyDescriptor", "properties", "constructor", "value", "enumerable", "writable", "configurable", "create", "xmlDecodeRegex", "utils", "nop", "promiseImmediate", "Promise", "resolve", "global", "setImmediate", "setTimeout", "dateToExcel", "d", "date1904", "getTime", "excelToDate", "v", "millisecondSinceEpoch", "Math", "round", "Date", "parsePath", "filepath", "last", "lastIndexOf", "path", "substring", "name", "getRelsPath", "xmlEncode", "text", "regexResult", "exec", "result", "escape", "lastIndex", "index", "length", "charCode", "charCodeAt", "xmlDecode", "replace", "c", "validInt", "parseInt", "Number", "isNaN", "isDateFmt", "fmt", "match", "exists", "access", "constants", "F_OK", "err", "toIsoDateString", "dt", "toIsoString", "subsstr", "parseBoolean", "module", "exports"], "sources": ["../../../lib/utils/utils.js"], "sourcesContent": ["const fs = require('fs');\n\n// useful stuff\nconst inherits = function(cls, superCtor, statics, prototype) {\n  // eslint-disable-next-line no-underscore-dangle\n  cls.super_ = superCtor;\n\n  if (!prototype) {\n    prototype = statics;\n    statics = null;\n  }\n\n  if (statics) {\n    Object.keys(statics).forEach(i => {\n      Object.defineProperty(cls, i, Object.getOwnPropertyDescriptor(statics, i));\n    });\n  }\n\n  const properties = {\n    constructor: {\n      value: cls,\n      enumerable: false,\n      writable: false,\n      configurable: true,\n    },\n  };\n  if (prototype) {\n    Object.keys(prototype).forEach(i => {\n      properties[i] = Object.getOwnPropertyDescriptor(prototype, i);\n    });\n  }\n\n  cls.prototype = Object.create(superCtor.prototype, properties);\n};\n\n// eslint-disable-next-line no-control-regex\nconst xmlDecodeRegex = /[<>&'\"\\x7F\\x00-\\x08\\x0B-\\x0C\\x0E-\\x1F]/;\nconst utils = {\n  nop() {},\n  promiseImmediate(value) {\n    return new Promise(resolve => {\n      if (global.setImmediate) {\n        setImmediate(() => {\n          resolve(value);\n        });\n      } else {\n        // poorman's setImmediate - must wait at least 1ms\n        setTimeout(() => {\n          resolve(value);\n        }, 1);\n      }\n    });\n  },\n  inherits,\n  dateToExcel(d, date1904) {\n    return 25569 + ( d.getTime() / (24 * 3600 * 1000) ) - (date1904 ? 1462 : 0);\n  },\n  excelToDate(v, date1904) {\n    const millisecondSinceEpoch = Math.round((v - 25569 + (date1904 ? 1462 : 0)) * 24 * 3600 * 1000);\n    return new Date(millisecondSinceEpoch);\n  },\n  parsePath(filepath) {\n    const last = filepath.lastIndexOf('/');\n    return {\n      path: filepath.substring(0, last),\n      name: filepath.substring(last + 1),\n    };\n  },\n  getRelsPath(filepath) {\n    const path = utils.parsePath(filepath);\n    return `${path.path}/_rels/${path.name}.rels`;\n  },\n  xmlEncode(text) {\n    const regexResult = xmlDecodeRegex.exec(text);\n    if (!regexResult) return text;\n\n    let result = '';\n    let escape = '';\n    let lastIndex = 0;\n    let i = regexResult.index;\n    for (; i < text.length; i++) {\n      const charCode = text.charCodeAt(i);\n      switch (charCode) {\n        case 34: // \"\n          escape = '&quot;';\n          break;\n        case 38: // &\n          escape = '&amp;';\n          break;\n        case 39: // '\n          escape = '&apos;';\n          break;\n        case 60: // <\n          escape = '&lt;';\n          break;\n        case 62: // >\n          escape = '&gt;';\n          break;\n        case 127:\n          escape = '';\n          break;\n        default: {\n          if (charCode <= 31 && (charCode <= 8 || (charCode >= 11 && charCode !== 13))) {\n            escape = '';\n            break;\n          }\n          continue;\n        }\n      }\n      if (lastIndex !== i) result += text.substring(lastIndex, i);\n      lastIndex = i + 1;\n      if (escape) result += escape;\n    }\n    if (lastIndex !== i) return result + text.substring(lastIndex, i);\n    return result;\n  },\n  xmlDecode(text) {\n    return text.replace(/&([a-z]*);/g, c => {\n      switch (c) {\n        case '&lt;':\n          return '<';\n        case '&gt;':\n          return '>';\n        case '&amp;':\n          return '&';\n        case '&apos;':\n          return '\\'';\n        case '&quot;':\n          return '\"';\n        default:\n          return c;\n      }\n    });\n  },\n  validInt(value) {\n    const i = parseInt(value, 10);\n    return !Number.isNaN(i) ? i : 0;\n  },\n\n  isDateFmt(fmt) {\n    if (!fmt) {\n      return false;\n    }\n\n    // must remove all chars inside quotes and []\n    fmt = fmt.replace(/\\[[^\\]]*]/g, '');\n    fmt = fmt.replace(/\"[^\"]*\"/g, '');\n    // then check for date formatting chars\n    const result = fmt.match(/[ymdhMsb]+/) !== null;\n    return result;\n  },\n\n  fs: {\n    exists(path) {\n      return new Promise(resolve => {\n        fs.access(path, fs.constants.F_OK, err => {\n          resolve(!err);\n        });\n      });\n    },\n  },\n\n  toIsoDateString(dt) {\n    return dt.toIsoString().subsstr(0, 10);\n  },\n\n  parseBoolean(value) {\n    return value === true || value === 'true' || value === 1 || value === '1';\n  },\n};\n\nmodule.exports = utils;\n"], "mappings": ";;AAAA,MAAMA,EAAE,GAAGC,OAAO,CAAC,IAAI,CAAC;;AAExB;AACA,MAAMC,QAAQ,GAAG,SAAAA,CAASC,GAAG,EAAEC,SAAS,EAAEC,OAAO,EAAEC,SAAS,EAAE;EAC5D;EACAH,GAAG,CAACI,MAAM,GAAGH,SAAS;EAEtB,IAAI,CAACE,SAAS,EAAE;IACdA,SAAS,GAAGD,OAAO;IACnBA,OAAO,GAAG,IAAI;EAChB;EAEA,IAAIA,OAAO,EAAE;IACXG,MAAM,CAACC,IAAI,CAACJ,OAAO,CAAC,CAACK,OAAO,CAACC,CAAC,IAAI;MAChCH,MAAM,CAACI,cAAc,CAACT,GAAG,EAAEQ,CAAC,EAAEH,MAAM,CAACK,wBAAwB,CAACR,OAAO,EAAEM,CAAC,CAAC,CAAC;IAC5E,CAAC,CAAC;EACJ;EAEA,MAAMG,UAAU,GAAG;IACjBC,WAAW,EAAE;MACXC,KAAK,EAAEb,GAAG;MACVc,UAAU,EAAE,KAAK;MACjBC,QAAQ,EAAE,KAAK;MACfC,YAAY,EAAE;IAChB;EACF,CAAC;EACD,IAAIb,SAAS,EAAE;IACbE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACI,OAAO,CAACC,CAAC,IAAI;MAClCG,UAAU,CAACH,CAAC,CAAC,GAAGH,MAAM,CAACK,wBAAwB,CAACP,SAAS,EAAEK,CAAC,CAAC;IAC/D,CAAC,CAAC;EACJ;EAEAR,GAAG,CAACG,SAAS,GAAGE,MAAM,CAACY,MAAM,CAAChB,SAAS,CAACE,SAAS,EAAEQ,UAAU,CAAC;AAChE,CAAC;;AAED;AACA,MAAMO,cAAc,GAAG,wCAAwC;AAC/D,MAAMC,KAAK,GAAG;EACZC,GAAGA,CAAA,EAAG,CAAC,CAAC;EACRC,gBAAgBA,CAACR,KAAK,EAAE;IACtB,OAAO,IAAIS,OAAO,CAACC,OAAO,IAAI;MAC5B,IAAIC,MAAM,CAACC,YAAY,EAAE;QACvBA,YAAY,CAAC,MAAM;UACjBF,OAAO,CAACV,KAAK,CAAC;QAChB,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACAa,UAAU,CAAC,MAAM;UACfH,OAAO,CAACV,KAAK,CAAC;QAChB,CAAC,EAAE,CAAC,CAAC;MACP;IACF,CAAC,CAAC;EACJ,CAAC;EACDd,QAAQ;EACR4B,WAAWA,CAACC,CAAC,EAAEC,QAAQ,EAAE;IACvB,OAAO,KAAK,GAAKD,CAAC,CAACE,OAAO,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI,CAAG,IAAID,QAAQ,GAAG,IAAI,GAAG,CAAC,CAAC;EAC7E,CAAC;EACDE,WAAWA,CAACC,CAAC,EAAEH,QAAQ,EAAE;IACvB,MAAMI,qBAAqB,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,CAAC,GAAG,KAAK,IAAIH,QAAQ,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC;IAChG,OAAO,IAAIO,IAAI,CAACH,qBAAqB,CAAC;EACxC,CAAC;EACDI,SAASA,CAACC,QAAQ,EAAE;IAClB,MAAMC,IAAI,GAAGD,QAAQ,CAACE,WAAW,CAAC,GAAG,CAAC;IACtC,OAAO;MACLC,IAAI,EAAEH,QAAQ,CAACI,SAAS,CAAC,CAAC,EAAEH,IAAI,CAAC;MACjCI,IAAI,EAAEL,QAAQ,CAACI,SAAS,CAACH,IAAI,GAAG,CAAC;IACnC,CAAC;EACH,CAAC;EACDK,WAAWA,CAACN,QAAQ,EAAE;IACpB,MAAMG,IAAI,GAAGtB,KAAK,CAACkB,SAAS,CAACC,QAAQ,CAAC;IACtC,OAAQ,GAAEG,IAAI,CAACA,IAAK,UAASA,IAAI,CAACE,IAAK,OAAM;EAC/C,CAAC;EACDE,SAASA,CAACC,IAAI,EAAE;IACd,MAAMC,WAAW,GAAG7B,cAAc,CAAC8B,IAAI,CAACF,IAAI,CAAC;IAC7C,IAAI,CAACC,WAAW,EAAE,OAAOD,IAAI;IAE7B,IAAIG,MAAM,GAAG,EAAE;IACf,IAAIC,MAAM,GAAG,EAAE;IACf,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAI3C,CAAC,GAAGuC,WAAW,CAACK,KAAK;IACzB,OAAO5C,CAAC,GAAGsC,IAAI,CAACO,MAAM,EAAE7C,CAAC,EAAE,EAAE;MAC3B,MAAM8C,QAAQ,GAAGR,IAAI,CAACS,UAAU,CAAC/C,CAAC,CAAC;MACnC,QAAQ8C,QAAQ;QACd,KAAK,EAAE;UAAE;UACPJ,MAAM,GAAG,QAAQ;UACjB;QACF,KAAK,EAAE;UAAE;UACPA,MAAM,GAAG,OAAO;UAChB;QACF,KAAK,EAAE;UAAE;UACPA,MAAM,GAAG,QAAQ;UACjB;QACF,KAAK,EAAE;UAAE;UACPA,MAAM,GAAG,MAAM;UACf;QACF,KAAK,EAAE;UAAE;UACPA,MAAM,GAAG,MAAM;UACf;QACF,KAAK,GAAG;UACNA,MAAM,GAAG,EAAE;UACX;QACF;UAAS;YACP,IAAII,QAAQ,IAAI,EAAE,KAAKA,QAAQ,IAAI,CAAC,IAAKA,QAAQ,IAAI,EAAE,IAAIA,QAAQ,KAAK,EAAG,CAAC,EAAE;cAC5EJ,MAAM,GAAG,EAAE;cACX;YACF;YACA;UACF;MACF;MACA,IAAIC,SAAS,KAAK3C,CAAC,EAAEyC,MAAM,IAAIH,IAAI,CAACJ,SAAS,CAACS,SAAS,EAAE3C,CAAC,CAAC;MAC3D2C,SAAS,GAAG3C,CAAC,GAAG,CAAC;MACjB,IAAI0C,MAAM,EAAED,MAAM,IAAIC,MAAM;IAC9B;IACA,IAAIC,SAAS,KAAK3C,CAAC,EAAE,OAAOyC,MAAM,GAAGH,IAAI,CAACJ,SAAS,CAACS,SAAS,EAAE3C,CAAC,CAAC;IACjE,OAAOyC,MAAM;EACf,CAAC;EACDO,SAASA,CAACV,IAAI,EAAE;IACd,OAAOA,IAAI,CAACW,OAAO,CAAC,aAAa,EAAEC,CAAC,IAAI;MACtC,QAAQA,CAAC;QACP,KAAK,MAAM;UACT,OAAO,GAAG;QACZ,KAAK,MAAM;UACT,OAAO,GAAG;QACZ,KAAK,OAAO;UACV,OAAO,GAAG;QACZ,KAAK,QAAQ;UACX,OAAO,IAAI;QACb,KAAK,QAAQ;UACX,OAAO,GAAG;QACZ;UACE,OAAOA,CAAC;MACZ;IACF,CAAC,CAAC;EACJ,CAAC;EACDC,QAAQA,CAAC9C,KAAK,EAAE;IACd,MAAML,CAAC,GAAGoD,QAAQ,CAAC/C,KAAK,EAAE,EAAE,CAAC;IAC7B,OAAO,CAACgD,MAAM,CAACC,KAAK,CAACtD,CAAC,CAAC,GAAGA,CAAC,GAAG,CAAC;EACjC,CAAC;EAEDuD,SAASA,CAACC,GAAG,EAAE;IACb,IAAI,CAACA,GAAG,EAAE;MACR,OAAO,KAAK;IACd;;IAEA;IACAA,GAAG,GAAGA,GAAG,CAACP,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;IACnCO,GAAG,GAAGA,GAAG,CAACP,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;IACjC;IACA,MAAMR,MAAM,GAAGe,GAAG,CAACC,KAAK,CAAC,YAAY,CAAC,KAAK,IAAI;IAC/C,OAAOhB,MAAM;EACf,CAAC;EAEDpD,EAAE,EAAE;IACFqE,MAAMA,CAACzB,IAAI,EAAE;MACX,OAAO,IAAInB,OAAO,CAACC,OAAO,IAAI;QAC5B1B,EAAE,CAACsE,MAAM,CAAC1B,IAAI,EAAE5C,EAAE,CAACuE,SAAS,CAACC,IAAI,EAAEC,GAAG,IAAI;UACxC/C,OAAO,CAAC,CAAC+C,GAAG,CAAC;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC;EAEDC,eAAeA,CAACC,EAAE,EAAE;IAClB,OAAOA,EAAE,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC;EACxC,CAAC;EAEDC,YAAYA,CAAC9D,KAAK,EAAE;IAClB,OAAOA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,CAAC,IAAIA,KAAK,KAAK,GAAG;EAC3E;AACF,CAAC;AAED+D,MAAM,CAACC,OAAO,GAAG1D,KAAK"}