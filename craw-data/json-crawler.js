const fs = require('fs');
const axios = require('axios');

class JsonCrawler {
    constructor() {
        this.headers = {
            'accept': '*/*',
            'accept-language': 'vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7,ja;q=0.6',
            'cache-control': 'no-cache',
            'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'origin': 'https://nfrccps.com',
            'referer': 'https://nfrccps.com/',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
        };
        
        this.batchSize = 50;
        this.processedCount = 240;
    }

    truncateIfTooLarge(data, maxSize = 1000000) {
        try {
            const jsonString = JSON.stringify(data);
            if (jsonString.length > maxSize) {
                console.log(`Response too large (${jsonString.length} chars), truncating...`);

                if (data && data.contractorPoints) {
                    const contractorCount = Object.keys(data.contractorPoints).length;
                    console.log(`Found ${contractorCount} contractors, keeping metadata only`);

                    return {
                        contractorCount,
                        contractorNames: Object.keys(data.contractorPoints),
                        truncated: true,
                        originalSize: jsonString.length,
                        message: "Response too large, saved contractor names only"
                    };
                }

                return {
                    truncated: true,
                    originalSize: jsonString.length,
                    message: "Response too large, could not save"
                };
            }
            return data;
        } catch (error) {
            console.log(`Error processing response: ${error.message}`);
            return {
                error: "Could not process response",
                message: error.message,
                truncated: true
            };
        }
    }

    async crawlPostcode(postcode) {
        try {
            console.log(`Crawling postcode: ${postcode}`);

            const response = await axios.post(
                'https://crm.nfrccps.com/index.php?entryPoint=SearchRooferPage&action=search',
                `type=postcode&val=${postcode}&radius=50`,
                { headers: this.headers }
            );

            const processedResponse = this.truncateIfTooLarge(response.data);

            return {
                postcode,
                timestamp: new Date().toISOString(),
                hasData: !!(response.data && response.data.contractorPoints),
                rawResponse: processedResponse
            };

        } catch (error) {
            console.error(`Error crawling postcode ${postcode}:`, error.message);
            return {
                postcode,
                timestamp: new Date().toISOString(),
                hasData: false,
                rawResponse: null,
                error: error.message
            };
        }
    }

    appendToJsonFile(result, filename) {
        try {
            if (!fs.existsSync(filename)) {
                const initialData = {
                    timestamp: new Date().toISOString(),
                    totalPostcodes: 0,
                    data: []
                };
                fs.writeFileSync(filename, JSON.stringify(initialData, null, 2));
            }

            const existingData = JSON.parse(fs.readFileSync(filename));
            existingData.data.push(result);
            existingData.totalPostcodes = existingData.data.length;
            existingData.lastUpdated = new Date().toISOString();

            // Try to write, if fails due to size, write to separate file
            try {
                fs.writeFileSync(filename, JSON.stringify(existingData, null, 2));
            } catch (writeError) {
                if (writeError.message.includes('Invalid string length')) {
                    console.log(`Main file too large, writing ${result.postcode} to separate file...`);
                    const separateFile = filename.replace('.json', `_${result.postcode}.json`);
                    fs.writeFileSync(separateFile, JSON.stringify({
                        postcode: result.postcode,
                        timestamp: result.timestamp,
                        data: result
                    }, null, 2));
                    console.log(`Saved to separate file: ${separateFile}`);
                } else {
                    throw writeError;
                }
            }

        } catch (error) {
            console.error(`Error saving ${result.postcode}:`, error.message);
            // Save to error file
            const errorFile = filename.replace('.json', '_errors.json');
            const errorData = {
                postcode: result.postcode,
                timestamp: new Date().toISOString(),
                error: error.message,
                result: {
                    postcode: result.postcode,
                    timestamp: result.timestamp,
                    hasData: result.hasData,
                    error: result.error || 'Could not save to main file'
                }
            };

            try {
                if (fs.existsSync(errorFile)) {
                    const existingErrors = JSON.parse(fs.readFileSync(errorFile));
                    existingErrors.push(errorData);
                    fs.writeFileSync(errorFile, JSON.stringify(existingErrors, null, 2));
                } else {
                    fs.writeFileSync(errorFile, JSON.stringify([errorData], null, 2));
                }
                console.log(`Saved error data to: ${errorFile}`);
            } catch (errorSaveError) {
                console.error(`Could not save error data:`, errorSaveError.message);
            }
        }
    }

    forceGarbageCollection() {
        if (global.gc) {
            global.gc();
        }
    }

    async run() {
        try {
            const postcodes = JSON.parse(fs.readFileSync('./postcodes.json'));
            const uniquePostcodes = [...new Set(postcodes.map(p => p.postcode))];
            console.log(`Found ${uniquePostcodes.length} unique postcodes`);

            if (!fs.existsSync('./exports')) {
                fs.mkdirSync('./exports');
            }

            const filename = `./exports/raw_data_${new Date().toISOString().split('T')[0]}.json`;

            for (let i = this.processedCount; i < uniquePostcodes.length; i++) {
                const postcode = uniquePostcodes[i];
                this.processedCount++;
                
                console.log(`[${this.processedCount}/${uniquePostcodes.length}] Crawling: ${postcode}`);
                
                const result = await this.crawlPostcode(postcode);
                this.appendToJsonFile(result, filename);
                
                if (this.processedCount % this.batchSize === 0) {
                    console.log(`Processed ${this.processedCount} postcodes, forcing garbage collection...`);
                    this.forceGarbageCollection();
                }
                
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            console.log(`Done! Crawled ${this.processedCount} postcodes to ${filename}`);

        } catch (error) {
            console.error('Error:', error);
            console.log(`Processed ${this.processedCount} postcodes before error`);
            process.exit(1);
        }
    }
}

const crawler = new JsonCrawler();
crawler.run();
