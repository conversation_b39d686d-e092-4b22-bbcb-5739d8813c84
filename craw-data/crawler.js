const fs = require('fs');
const axios = require('axios');
const ExcelJS = require('exceljs');
const { JSDOM } = require('jsdom');

class ContractorCrawler {
    constructor() {
        this.headers = {
            'accept': '*/*',
            'accept-language': 'vi-VN,vi;q=0.9,en-US;q=0.8,en;q=0.7,ja;q=0.6',
            'cache-control': 'no-cache',
            'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'origin': 'https://nfrccps.com',
            'referer': 'https://nfrccps.com/',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        };

        this.excelColumns = [
            { header: 'Postcode', key: 'postcode', width: 15 },
            { header: 'Company Name', key: 'name', width: 30 },
            { header: 'Image', key: 'image', width: 20 },
            { header: 'Address', key: 'address', width: 50 },
            { header: 'Distance', key: 'distance', width: 15 },
            { header: 'Phone', key: 'phone', width: 15 },
            { header: 'Website', key: 'website', width: 30 },
            { header: 'Disciplines', key: 'disciplines', width: 30 },
            { header: 'Status', key: 'status', width: 10 },
            { header: 'Company Bio', key: 'bio', width: 100 },
            { header: 'Latitude', key: 'lat', width: 15 },
            { header: 'Longitude', key: 'lon', width: 15 },
            { header: 'Raw Data', key: 'rawData', width: 150 }
        ];

        this.workbook = new ExcelJS.Workbook();
        this.worksheet = this.workbook.addWorksheet('Contractors');
        this.worksheet.columns = this.excelColumns;
    }

    parseContractorData(html) {
        const dom = new JSDOM(html);
        const doc = dom.window.document;

        return {
            image: this.getImageData(doc),
            address: this.getTextContent(doc, "Address:"),
            distance: this.getTextContent(doc, "Distance From Point:"),
            phone: this.getTextContent(doc, "Phone:"),
            website: this.getTextContent(doc, "Website:"),
            disciplines: this.getTextContent(doc, "Disciplines:"),
            status: this.getTextContent(doc, "Status:"),
            bio: this.getTextContent(doc, "Company Bio:")
        };
    }

    getImageData(doc) {
        const imgElement = doc.querySelector('label img');
        if (imgElement && imgElement.src && imgElement.src.startsWith('data:image/')) {
            return imgElement.src;
        }
        return '';
    }

    getTextContent(doc, label) {
        const labelElement = Array.from(doc.querySelectorAll('label')).find(el =>
            el.textContent.includes(label) && !el.querySelector('img')
        );
        return labelElement ? labelElement.nextSibling.textContent.trim() : '';
    }

    async crawlPostcode(postcode) {
        try {
            console.log(`Crawling postcode: ${postcode}`);

            const response = await axios.post(
                'https://crm.nfrccps.com/index.php?entryPoint=SearchRooferPage&action=search',
                `type=postcode&val=${postcode}&radius=50`,
                { headers: this.headers }
            );

            return {
                postcode,
                timestamp: new Date().toISOString(),
                hasData: !!(response.data && response.data.contractorPoints),
                rawResponse: response.data || null,
                contractors: this.parseContractorsForExcel(postcode, response.data)
            };

        } catch (error) {
            console.error(`Error crawling postcode ${postcode}:`, error.message);
            return {
                postcode,
                timestamp: new Date().toISOString(),
                hasData: false,
                rawResponse: null,
                error: error.message,
                contractors: [{
                    postcode,
                    name: '',
                    image: '',
                    address: '',
                    distance: '',
                    phone: '',
                    website: '',
                    disciplines: '',
                    status: '',
                    bio: '',
                    lat: '',
                    lon: '',
                    rawData: JSON.stringify({ error: error.message })
                }]
            };
        }
    }

    parseContractorsForExcel(postcode, responseData) {
        if (!responseData || !responseData.contractorPoints) {
            console.log(`No data found for postcode: ${postcode}`);
            return [{
                postcode,
                name: '',
                image: '',
                address: '',
                distance: '',
                phone: '',
                website: '',
                disciplines: '',
                status: '',
                bio: '',
                lat: '',
                lon: '',
                rawData: JSON.stringify(responseData || {})
            }];
        }

        const contractors = [];
        for (const [name, data] of Object.entries(responseData.contractorPoints)) {
            const parsedData = this.parseContractorData(data.body);
            contractors.push({
                postcode,
                name,
                ...parsedData,
                lat: data.lat || '',
                lon: data.lon || '',
                rawData: JSON.stringify({
                    contractorName: name,
                    contractorData: data,
                    fullResponse: responseData
                })
            });
        }

        return contractors;
    }

    addToExcel(contractors) {
        contractors.forEach(contractor => {
            this.worksheet.addRow({
                postcode: contractor.postcode || '',
                name: contractor.name || '',
                image: contractor.image || '',
                address: contractor.address || '',
                distance: contractor.distance || '',
                phone: contractor.phone || '',
                website: contractor.website || '',
                disciplines: contractor.disciplines || '',
                status: contractor.status || '',
                bio: contractor.bio || '',
                lat: contractor.lat || '',
                lon: contractor.lon || '',
                rawData: contractor.rawData || ''
            });
        });
    }

    saveJsonBackup(rawData, filename) {
        const jsonData = {
            timestamp: new Date().toISOString(),
            totalPostcodes: rawData.length,
            data: rawData
        };

        const jsonFilename = filename.replace('.xlsx', '.json');
        fs.writeFileSync(jsonFilename, JSON.stringify(jsonData, null, 2));
        console.log(`JSON backup saved: ${jsonFilename}`);
    }

    async run() {
        try {
            const postcodes = JSON.parse(fs.readFileSync('./postcodes.json'));
            const uniquePostcodes = [...new Set(postcodes.map(p => p.postcode))];
            console.log(`Found ${uniquePostcodes.length} unique postcodes`);

            const rawDataResults = [];
            const allContractors = [];

            for (const postcode of uniquePostcodes) {
                const result = await this.crawlPostcode(postcode);
                rawDataResults.push(result);
                allContractors.push(...result.contractors);
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            if (!fs.existsSync('./exports')) {
                fs.mkdirSync('./exports');
            }

            const filename = `./exports/contractors_${new Date().toISOString().split('T')[0]}.xlsx`;

            this.saveJsonBackup(rawDataResults, filename);
            this.addToExcel(allContractors);
            await this.workbook.xlsx.writeFile(filename);

            console.log(`Done! Exported ${allContractors.length} contractors to ${filename}`);
            console.log(`Raw data saved for ${rawDataResults.length} postcodes`);

        } catch (error) {
            console.error('Error:', error);
            process.exit(1);
        }
    }
}

const crawler = new ContractorCrawler();
crawler.run();
